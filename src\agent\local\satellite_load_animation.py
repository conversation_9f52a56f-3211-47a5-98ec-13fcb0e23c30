"""
卫星负载动态可视化程序
生成200个时隙的动画，显示卫星位置变化和负载变化
"""

import sys
import os
import numpy as np
from pathlib import Path
import yaml
import matplotlib.pyplot as plt
import matplotlib.colors as mcolors
import matplotlib.animation as animation
import cartopy.crs as ccrs
import cartopy.feature as cfeature
from typing import Dict, List, Tuple
import json
from datetime import datetime, timedelta
import warnings
warnings.filterwarnings('ignore')

# 添加项目根目录到路径
sys.path.append(str(Path(__file__).parent.parent.parent.parent))

# 导入必要模块
from src.env.physics_layer.orbital import OrbitalUpdater
from src.env.physics_layer.communication_refactored import CommunicationManager
from src.env.physics_layer.task_generator import TaskGenerator
from src.env.physics_layer.task_tracking import TaskTrackingSystem, Task, TaskStatus
from src.env.satellite_cloud.satellite import Satellite, SatelliteTask


class SatelliteLoadAnimator:
    """卫星负载动画生成器"""
    
    def __init__(self, config_path: str, max_timeslots: int = 1000):
        """
        初始化动画生成器
        
        Args:
            config_path: 配置文件路径
            max_timeslots: 最大时隙数
        """
        self.config_path = config_path
        with open(config_path, 'r', encoding='utf-8') as f:
            self.config = yaml.safe_load(f)
        
        self.max_timeslots = max_timeslots
        self.timeslot_duration = self.config['system']['timeslot_duration_s']
        
        # 初始化轨道管理器
        data_file = Path(config_path).parent.parent / 'env_data' / 'satellite_data72_1.csv'
        self.orbital_updater = OrbitalUpdater(
            data_file=str(data_file),
            config_file=config_path
        )
        
        # 初始化通信管理器
        self.comm_manager = CommunicationManager(
            orbital_updater=self.orbital_updater,
            config_file=config_path
        )
        
        # 初始化任务生成器
        self.task_generator = TaskGenerator()
        ground_stations_path = Path(config_path).parent.parent / 'env_data' / 'global_ground_stations.csv'
        self.task_generator.load_locations_from_csv(str(ground_stations_path))
        
        # 初始化卫星
        self.satellites = self._initialize_satellites()
        
        # 存储所有时隙的数据
        self.all_timeslot_data = {}
        
        # 新增：记录每个卫星在当前时隙接收的任务总工作量
        self.satellite_current_workload = {i: 0.0 for i in range(len(self.satellites))}
        
        # 动画相关
        self.fig = None
        self.ax = None
        self.scatter = None
        self.title = None
        self.info_text = None
        self.time_text = None
        
    def _initialize_satellites(self) -> List[Satellite]:
        """初始化72个卫星"""
        satellites = []
        satellite_ids_raw = self.orbital_updater.get_satellite_ids(1)
        
        actual_satellite_ids = []
        for sid in satellite_ids_raw:
            if isinstance(sid, str) and sid.startswith('sat_'):
                actual_satellite_ids.append(int(sid.split('_')[1]))
            else:
                actual_satellite_ids.append(int(sid))
        actual_satellite_ids = sorted(actual_satellite_ids)
        
        self.satellite_id_mapping = {}  # 数组索引 -> 实际卫星ID
        
        for idx, actual_sat_id in enumerate(actual_satellite_ids):
            satellite = Satellite(
                satellite_id=actual_sat_id,
                config_path=self.config_path,
                orbital_updater=self.orbital_updater,
                comm_manager=self.comm_manager
            )
            satellites.append(satellite)
            self.satellite_id_mapping[idx] = actual_sat_id
        
        print(f"初始化了 {len(satellites)} 个卫星")
        return satellites
    
    def run_simulation(self):
        """运行仿真并收集所有时隙数据"""
        print(f"\n开始运行仿真，共 {self.max_timeslots} 个时隙...")
        
        for timeslot in range(1, self.max_timeslots + 1):
            current_time = (timeslot - 1) * self.timeslot_duration
            
            # 重置当前时隙的工作量记录
            self.satellite_current_workload = {i: 0.0 for i in range(len(self.satellites))}
            
            # 获取卫星位置
            satellites_dict = self.orbital_updater.get_satellites_at_time(timeslot)
            
            # 构建可见性矩阵
            sg_visibility_matrix, sg_distance_matrix_main = self.orbital_updater.build_satellite_ground_visibility_matrix(
                satellites_dict, 
                self.orbital_updater.ground_stations
            )
            
            # 生成任务
            new_tasks = self._generate_tasks(timeslot, current_time)
            
            # 分配任务到卫星
            self._assign_tasks_to_satellites(new_tasks, sg_visibility_matrix, current_time)
            
            # 处理卫星任务
            for satellite in self.satellites:
                satellite.schedule_tasks(current_time)
                satellite.process_tasks(current_time)
            
            # 收集数据
            self._collect_timeslot_data(timeslot, satellites_dict, current_time)
            
            # 进度报告
            if timeslot % 20 == 0:
                print(f"  仿真进度: {timeslot}/{self.max_timeslots}")
        
        print("仿真完成，数据收集完毕")
    
    def _generate_tasks(self, timeslot: int, current_time: float) -> List[Task]:
        """生成任务"""
        all_tasks = []
        self.task_generator.current_time = current_time
        
        # 为所有地面站生成任务，确保全球覆盖
        for location in self.task_generator.locations:
            tasks = self.task_generator.generate_tasks_for_location(location, current_time)
            for task in tasks:
                task_obj = Task(
                    task_id=task.task_id,
                    type_id=task.type_id,
                    data_size_mb=task.data_size_mb,
                    complexity_cycles_per_bit=task.complexity_cycles_per_bit,
                    deadline_timestamp=task.deadline_timestamp,
                    priority=task.priority,
                    location_id=location.location_id,
                    coordinates=location.coordinates,
                    generation_time=current_time,
                    geography=location.geography,
                    scale=location.scale,
                    functional_type=location.functional_type
                )
                all_tasks.append(task_obj)
        
        return all_tasks
    
    def _assign_tasks_to_satellites(self, tasks: List[Task], 
                                   sg_visibility: np.ndarray,
                                   current_time: float):
        """分配任务到卫星"""
        # 获取当前时隙的卫星位置
        timeslot = int(current_time / self.timeslot_duration) + 1
        satellites_dict = self.orbital_updater.get_satellites_at_time(timeslot)
        
        # 使用传入的可见性矩阵，避免重复构建
        sg_distance_matrix = self.orbital_updater.build_satellite_ground_visibility_matrix(
            satellites_dict, self.orbital_updater.ground_stations
        )[1]  # 只获取距离矩阵
        
        # 获取卫星ID列表（与可见性矩阵的行对应）
        satellite_ids_in_matrix = list(satellites_dict.keys())
        
        for task in tasks:
            ground_id = task.location_id - 1
            
            if ground_id < 0 or ground_id >= sg_visibility.shape[1]:
                continue
            
            # 获取该地面站可见的卫星
            visible_satellite_indices = np.where(sg_visibility[:, ground_id])[0]
            
            if len(visible_satellite_indices) == 0:
                continue
            
            # 从距离矩阵中获取到可见卫星的距离
            distances_to_visible = sg_distance_matrix[visible_satellite_indices, ground_id]
            
            # 选择距离最近的卫星（在可见性矩阵中的索引）
            closest_matrix_idx = visible_satellite_indices[np.argmin(distances_to_visible)]
            
            # 获取实际的卫星ID
            actual_satellite_id = satellite_ids_in_matrix[closest_matrix_idx]
            
            # 找到该卫星在self.satellites列表中的索引
            best_satellite = None
            for idx, sat_id in self.satellite_id_mapping.items():
                if sat_id == actual_satellite_id:
                    best_satellite = idx
                    break
            
            if best_satellite is None:
                continue
            
            # 创建卫星任务
            data_size_bits = task.data_size_mb * self.config['communication']['mb_to_bits']
            complexity = task.complexity_cycles_per_bit * data_size_bits
            
            sat_task = SatelliteTask(
                task_id=f"task_{task.task_id}",
                priority=float(task.priority),
                deadline=float(task.deadline_timestamp),
                data_size=task.data_size_mb,
                complexity=complexity,
                drop_penalty=self.config['computation']['default_drop_penalty'] * task.priority,
                arrival_time=current_time
            )
            
            # 添加到卫星
            self.satellites[best_satellite].add_task(sat_task)
            
            # 记录该卫星在当前时隙接收的任务工作量（复杂度即总计算量）
            self.satellite_current_workload[best_satellite] += complexity
    
    def _collect_timeslot_data(self, timeslot: int, satellites_dict: Dict, current_time: float):
        """收集时隙数据"""
        positions = []
        loads = []
        
        # 计算当前时隙最大工作量用于归一化
        max_workload = max(self.satellite_current_workload.values()) if self.satellite_current_workload else 1.0
        if max_workload == 0:
            max_workload = 1.0
        
        # 确保所有卫星的数据都被收集，保持索引对应关系
        for idx, satellite in enumerate(self.satellites):
            actual_sat_id = self.satellite_id_mapping[idx]
            
            # 使用当前时隙的工作量作为负载指标（归一化到0-100）
            normalized_load = (self.satellite_current_workload[idx] / max_workload) * 100
            
            # 获取位置，确保每个卫星都有对应的位置数据
            if actual_sat_id in satellites_dict:
                sat_data = satellites_dict[actual_sat_id]
                positions.append([sat_data.longitude, sat_data.latitude])
                loads.append(normalized_load)
            else:
                # 如果某个卫星不在当前时隙的数据中，使用默认值
                print(f"警告: 时隙{timeslot}中找不到卫星{actual_sat_id}的位置数据")
                positions.append([0.0, 0.0])  # 使用默认位置
                loads.append(normalized_load)
        
        # 统计信息
        active_sats = len([l for l in loads if l > 0])
        high_load_sats = len([l for l in loads if l > 70])
        avg_load = np.mean(loads) if loads else 0
        max_load = np.max(loads) if loads else 0
        
        self.all_timeslot_data[timeslot] = {
            'positions': np.array(positions),
            'loads': np.array(loads),
            'time': current_time,
            'stats': {
                'active_satellites': active_sats,
                'high_load_satellites': high_load_sats,
                'average_load': avg_load,
                'max_load': max_load,
                'total_tasks_completed': sum(sat.total_tasks_processed for sat in self.satellites),
                'total_tasks_dropped': sum(sat.total_tasks_dropped for sat in self.satellites)
            }
        }
    
    def create_animation(self):
        """创建动画"""
        print("\n开始创建动画...")
        
        # 创建图形
        self.fig = plt.figure(figsize=(16, 9))
        self.ax = plt.axes(projection=ccrs.PlateCarree())
        
        # 设置地图
        self.ax.set_global()
        self.ax.add_feature(cfeature.LAND, facecolor='#e0e0e0')
        self.ax.add_feature(cfeature.OCEAN, facecolor='#c8e6ff', alpha=0.5)
        self.ax.add_feature(cfeature.COASTLINE, linewidth=0.5, color='#666666')
        self.ax.add_feature(cfeature.BORDERS, linewidth=0.3, alpha=0.5, color='#999999')
        
        # 添加网格线
        gl = self.ax.gridlines(draw_labels=True, linewidth=0.3, alpha=0.3, color='gray')
        gl.top_labels = False
        gl.right_labels = False
        
        # 颜色映射
        self.cmap = mcolors.LinearSegmentedColormap.from_list(
            'load_cmap',
            ['#00ff00', '#ffff00', '#ff8800', '#ff0000']  # 绿-黄-橙-红
        )
        
        # 初始化散点图
        first_data = self.all_timeslot_data[1]
        self.scatter = self.ax.scatter(
            first_data['positions'][:, 0],
            first_data['positions'][:, 1],
            c=first_data['loads'],
            s=80,
            cmap=self.cmap,
            vmin=0,
            vmax=100,
            transform=ccrs.PlateCarree(),
            edgecolors='black',
            linewidth=0.5,
            alpha=0.9
        )
        
        # 添加颜色条
        cbar = plt.colorbar(self.scatter, ax=self.ax, 
                           orientation='horizontal',
                           pad=0.05, shrink=0.7)
        cbar.set_label('当前时隙任务工作量 (%) [绿色=低, 红色=高]', fontsize=11)
        
        # 标题
        self.title = self.ax.set_title('', fontsize=14, fontweight='bold', pad=20)
        
        # 信息文本框
        self.info_text = self.ax.text(0.02, 0.95, '', 
                                      transform=self.ax.transAxes,
                                      bbox=dict(boxstyle='round', 
                                               facecolor='white', 
                                               alpha=0.9),
                                      fontsize=10,
                                      verticalalignment='top')
        
        # 时间文本框
        self.time_text = self.ax.text(0.98, 0.95, '',
                                      transform=self.ax.transAxes,
                                      bbox=dict(boxstyle='round',
                                               facecolor='yellow',
                                               alpha=0.9),
                                      fontsize=11,
                                      horizontalalignment='right',
                                      verticalalignment='top',
                                      fontweight='bold')
        
        # 创建动画
        anim = animation.FuncAnimation(
            self.fig,
            self.update_frame,
            frames=self.max_timeslots,
            interval=100,  # 每帧100毫秒
            blit=False,
            repeat=True
        )
        
        # 保存动画
        output_dir = Path('animation_output')
        output_dir.mkdir(exist_ok=True)
        
        # 保存为GIF
        print("  正在保存GIF动画...")
        writer = animation.PillowWriter(fps=10)
        anim.save(output_dir / 'satellite_load_animation.gif', 
                 writer=writer,
                 dpi=100)
        print(f"  GIF动画已保存到 {output_dir / 'satellite_load_animation.gif'}")
        
        # 保存为MP4（如果有ffmpeg）
        try:
            print("  尝试保存MP4视频...")
            writer = animation.FFMpegWriter(fps=10, bitrate=2000)
            anim.save(output_dir / 'satellite_load_animation.mp4', 
                     writer=writer,
                     dpi=100)
            print(f"  MP4视频已保存到 {output_dir / 'satellite_load_animation.mp4'}")
        except:
            print("  无法保存MP4（需要安装ffmpeg）")
        
        plt.close()
        print("\n动画创建完成！")
    
    def update_frame(self, frame):
        """更新动画帧"""
        timeslot = frame + 1
        data = self.all_timeslot_data[timeslot]
        
        # 更新散点位置和颜色
        self.scatter.set_offsets(data['positions'])
        self.scatter.set_array(data['loads'])
        
        # 更新标题
        self.title.set_text(f'LEO卫星星座当前时隙任务分配 - 时隙 {timeslot}/{self.max_timeslots}')
        
        # 更新统计信息
        stats = data['stats']
        info_text = (f"本时隙接收任务的卫星: {stats['active_satellites']}/72\n"
                    f"高任务量卫星(>70%): {stats['high_load_satellites']}\n"
                    f"平均任务量: {stats['average_load']:.1f}%\n"
                    f"最大任务量: {stats['max_load']:.1f}%\n"
                    f"total_tasks_completed: {stats['total_tasks_completed']}\n"
                    f"total_tasks_dropped: {stats['total_tasks_dropped']}")
        self.info_text.set_text(info_text)
        
        # 更新时间
        current_time = data['time']
        hours = int(current_time // 3600)
        minutes = int((current_time % 3600) // 60)
        seconds = int(current_time % 60)
        self.time_text.set_text(f'T+{hours:02d}:{minutes:02d}:{seconds:02d}')
        
        return self.scatter, self.title, self.info_text, self.time_text
    
    def create_snapshot_comparison(self):
        """创建关键时隙的快照对比图"""
        print("\n创建关键时隙快照...")
        
        key_timeslots = [1, 50, 100, 150, 200]
        
        fig = plt.figure(figsize=(20, 4))
        
        for idx, timeslot in enumerate(key_timeslots):
            ax = fig.add_subplot(1, 5, idx+1, projection=ccrs.PlateCarree())
            
            # 设置地图
            ax.set_global()
            ax.add_feature(cfeature.LAND, facecolor='#e0e0e0')
            ax.add_feature(cfeature.OCEAN, facecolor='#c8e6ff', alpha=0.5)
            ax.add_feature(cfeature.COASTLINE, linewidth=0.3)
            ax.add_feature(cfeature.BORDERS, linewidth=0.2, alpha=0.5)
            
            # 获取数据
            data = self.all_timeslot_data[timeslot]
            
            # 绘制卫星
            scatter = ax.scatter(
                data['positions'][:, 0],
                data['positions'][:, 1],
                c=data['loads'],
                s=50,
                cmap=self.cmap,
                vmin=0,
                vmax=100,
                transform=ccrs.PlateCarree(),
                edgecolors='black',
                linewidth=0.3,
                alpha=0.9
            )
            
            # 标题
            ax.set_title(f'时隙 {timeslot}\n'
                        f'平均负载: {data["stats"]["average_load"]:.1f}%',
                        fontsize=10)
        
        # 总标题
        fig.suptitle('卫星负载变化快照对比', fontsize=14, fontweight='bold')
        
        # 添加颜色条
        fig.subplots_adjust(right=0.92, wspace=0.05)
        cbar_ax = fig.add_axes([0.94, 0.3, 0.015, 0.4])
        cbar = plt.colorbar(scatter, cax=cbar_ax)
        cbar.set_label('负载 (%)', fontsize=10)
        
        # 保存
        output_dir = Path('animation_output')
        output_dir.mkdir(exist_ok=True)
        plt.savefig(output_dir / 'satellite_load_snapshots.png', 
                   dpi=150, bbox_inches='tight')
        print(f"  快照对比图已保存到 {output_dir / 'satellite_load_snapshots.png'}")
        plt.close()
    
    def save_statistics(self):
        """保存统计数据"""
        output_dir = Path('animation_output')
        output_dir.mkdir(exist_ok=True)
        
        # 提取时间序列数据
        timeslots = sorted(self.all_timeslot_data.keys())
        avg_loads = []
        max_loads = []
        active_sats = []
        
        for ts in timeslots:
            stats = self.all_timeslot_data[ts]['stats']
            avg_loads.append(stats['average_load'])
            max_loads.append(stats['max_load'])
            active_sats.append(stats['active_satellites'])
        
        # 绘制趋势图
        fig, axes = plt.subplots(3, 1, figsize=(12, 8))
        
        # 平均负载趋势
        axes[0].plot(timeslots, avg_loads, 'b-', linewidth=2)
        axes[0].set_ylabel('平均负载 (%)', fontsize=10)
        axes[0].set_title('卫星负载趋势分析', fontsize=12, fontweight='bold')
        axes[0].grid(True, alpha=0.3)
        
        # 最大负载趋势
        axes[1].plot(timeslots, max_loads, 'r-', linewidth=2)
        axes[1].set_ylabel('最大负载 (%)', fontsize=10)
        axes[1].grid(True, alpha=0.3)
        
        # 活跃卫星数量
        axes[2].plot(timeslots, active_sats, 'g-', linewidth=2)
        axes[2].set_ylabel('活跃卫星数', fontsize=10)
        axes[2].set_xlabel('时隙', fontsize=10)
        axes[2].grid(True, alpha=0.3)
        
        plt.tight_layout()
        plt.savefig(output_dir / 'load_trends.png', dpi=150, bbox_inches='tight')
        print(f"  趋势图已保存到 {output_dir / 'load_trends.png'}")
        plt.close()
        
        # 保存统计报告
        report = []
        report.append("=" * 60)
        report.append("卫星负载动画统计报告")
        report.append("=" * 60)
        report.append(f"总时隙数: {self.max_timeslots}")
        report.append(f"仿真时长: {self.max_timeslots * self.timeslot_duration}秒")
        report.append(f"平均负载范围: {min(avg_loads):.2f}% - {max(avg_loads):.2f}%")
        report.append(f"最大负载峰值: {max(max_loads):.2f}%")
        report.append(f"平均活跃卫星数: {np.mean(active_sats):.1f}")
        
        with open(output_dir / 'animation_statistics.txt', 'w', encoding='utf-8') as f:
            f.write('\n'.join(report))
        
        print(f"  统计报告已保存到 {output_dir / 'animation_statistics.txt'}")


def main():
    """主函数"""
    config_path = Path(__file__).parent.parent.parent / "env" / "physics_layer" / "config.yaml"
    
    # 创建动画生成器
    animator = SatelliteLoadAnimator(str(config_path), max_timeslots=1000)
    
    # 运行仿真
    animator.run_simulation()
    
    # 创建动画
    animator.create_animation()
    
    # 创建快照对比
    animator.create_snapshot_comparison()
    
    # 保存统计数据
    animator.save_statistics()
    
    print("\n所有任务完成！")
    print("输出文件位于 animation_output/ 目录")


if __name__ == "__main__":
    main()