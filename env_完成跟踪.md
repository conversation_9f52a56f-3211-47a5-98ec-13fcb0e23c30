# SPACE2环境模块完成情况跟踪

> 文档版本: v1.1.0
> 最后更新: 2025-08-09
> 项目路径: D:\paper\space\SPACE2

## 一、概述

### 1.1 项目背景
SPACE2是一个LEO卫星星座边缘-云协同计算仿真平台，基于PettingZoo框架支持多智能体强化学习算法。系统模拟72颗LEO卫星、420个地面用户终端和5个云计算中心在1441个时隙（7205秒）内的复杂交互。

### 1.2 当前完成的模块
| 模块名称 | 文件路径 | 完成状态 | 版本 |
|---------|---------|---------|------|
| 配置管理 | `src/env/physics_layer/config.yaml` | ✅ 完成 | v1.0 |
| 时间管理 | `src/env/Foundation_Layer/time_manager.py` | ✅ 完成 | v1.0 |
| 轨道动力学 | `src/env/physics_layer/orbital.py` | ✅ 完成 | v2.0 |
| 通信链路管理 | `src/env/physics_layer/communication_refactored.py` | ✅ 完成 | v1.0 |
| 错误处理 | `src/env/Foundation_Layer/error_handler.py` | ✅ 完成 | v1.0 |
| 日志管理 | `src/env/Foundation_Layer/logger_config.py` | ✅ 完成 | v1.0 |
| **环境数据** | `src/env/env_data/` | ✅ 完成 | v1.0 |
| ├─ 卫星轨道数据 | `satellite_data72_1.csv` | ✅ 完成 | - |
| ├─ 云中心数据 | `cloud_station.csv` | ✅ 完成 | - |
| ├─ 地面终端数据 | `global_ground_stations.csv` | ✅ 完成 | - |
| └─ 任务生成数据 | `task_results.json` | ✅ 完成 | - |
| **任务管理** | `src/env/physics_layer/` | ✅ 完成 | v1.0 |
| ├─ 任务生成器 | `task_generator.py` | ✅ 完成 | v1.0 |
| └─ 任务跟踪系统 | `task_tracking.py` | ✅ 完成 | v1.0 |

### 1.3 模块依赖关系
```
                    config.yaml
                         |
                         v
                  TimeManager
                         |
                         v
                 OrbitalUpdater ←── env_data/
                    ↓    ↑           ├─ satellite_data72_1.csv
                    ↓    ↑           ├─ cloud_station.csv
                    ↓    ↑           ├─ global_ground_stations.csv
                    ↓    ↑           └─ updated_global_ground_stations.csv
                    v    ↑
              CommunicationManager
                         ↑
                         ↑
                  TaskTrackingSystem ←── TaskGenerator
                         |                    ↑
                         |              env_data/
                         |              └─ updated_global_ground_stations.csv
                         v
                 [待开发模块]
              /        |        \
             v         v         v
    ComputationManager TaskScheduler Environment
```

## 二、配置管理 (config.yaml)

### 2.1 文件说明
- **位置**: `src/env/physics_layer/config.yaml`
- **作用**: 集中管理所有系统参数，避免硬编码，支持参数调优
- **格式**: YAML格式，支持注释和层级结构

### 2.2 主要配置项

#### 系统架构参数
```yaml
system:
  num_users: 420                    # 地面用户终端数量
  num_leo_satellites: 72            # LEO卫星数量
  leo_altitude_m: 1200000           # LEO卫星轨道高度 (m)
  earth_radius_m: 6371000           # 地球半径 (m)
  timeslot_duration_s: 4            # 时隙持续时间 (秒)
  total_timeslots: 1500             # 总时隙数
  visibility_threshold_m: 5500000   # 卫星间可见性阈值距离 (m)
  visibility_earth_m: 2500000       # 地面站-卫星可见性阈值距离 (m)
  cloud_visibility_threshold_m: 3300000  # 云中心-卫星可见性阈值距离 (m)
```

#### 通信模型参数
```yaml
communication:
  rf_carrier_freq_hz: 14000000000   # 射频载波频率 (14 GHz)
  b_us_hz: 100000000                # 用户-卫星上行带宽 (100 MHz)
  p_u_w: 5                          # 用户终端发射功率 (5W)
  p_su_w: 10                        # 卫星对用户发射功率 (10W)
  p_c_w: 50                         # 云中心发射功率 (50W)
  isl_tra_rate: 50000000000         # 星间链路传输速率 (50 Gbps)
  antenna_gain_db: 33.0             # 天线增益 (dB)
```

### 2.3 使用方法
```python
import yaml

# 加载配置
with open('config.yaml', 'r', encoding='utf-8') as f:
    config = yaml.safe_load(f)

# 访问参数
num_satellites = config['system']['num_leo_satellites']
carrier_freq = config['communication']['rf_carrier_freq_hz']
```

## 三、时间管理模块 (time_manager.py)

### 3.1 模块作用
统一管理仿真时间和物理时间的映射关系，提供时间上下文服务，确保所有模块时间同步。

### 3.2 核心类和方法

#### TimeContext类
```python
@dataclass
class TimeContext:
    simulation_step: int      # 仿真步数 (0-1440)
    physical_time: datetime   # 物理时间
    timeslot_seconds: float   # 时隙持续时间
    
    @property
    def total_seconds(self) -> float:
        """获取从仿真开始的总秒数"""
```

#### TimeManager类
```python
class TimeManager:
    def __init__(self, start_time: datetime, timeslot_duration: float, total_timeslots: int)
    def get_time_context(self, simulation_step: int) -> TimeContext
    def is_valid_step(self, step: int) -> bool
    def get_elapsed_time(self, from_step: int, to_step: int) -> float
```

### 3.3 接口调用示例
```python
from env.Foundation_Layer.time_manager import create_time_manager_from_config

# 从配置创建时间管理器
time_manager = create_time_manager_from_config(config)

# 获取特定时隙的时间上下文
context = time_manager.get_time_context(100)
print(f"时隙100对应时间: {context.physical_time}")
print(f"已经过秒数: {context.total_seconds}")
```

## 四、轨道动力学模块 (orbital.py)

### 4.1 模块作用
- 管理卫星轨道数据和位置计算
- 计算卫星间、卫星-地面、卫星-云的可见性矩阵
- 提供ECEF坐标系下的3D距离计算

### 4.2 数据源和格式
- **数据文件**: `src/env/env_data/satellite_data72_1.csv`
- **记录数**: 103,754条 (72卫星 × 1441时隙)
- **字段格式**: `satellite_ID, time_slot, time, lat, lon, light, state`

### 4.3 核心功能

#### 4.3.1 卫星位置获取
```python
def get_satellites_at_time(self, time_step: int) -> Dict[str, Satellite]
```
返回指定时隙所有卫星的位置和状态。

#### 4.3.2 可见性矩阵计算
```python
# 卫星间可见性 (72×72矩阵)
def build_visibility_matrix(satellites) -> Tuple[np.ndarray, np.ndarray]

# 卫星-地面可见性 (72×420矩阵)  
def build_satellite_ground_visibility_matrix(satellites, time_step) -> Tuple[np.ndarray, np.ndarray]

# 卫星-云可见性 (72×5矩阵)
def build_satellite_cloud_visibility_matrix(satellites, time_step) -> Tuple[np.ndarray, np.ndarray]
```

### 4.4 主要接口

#### 初始化
```python
from env.physics_layer.orbital import OrbitalUpdater

orbital = OrbitalUpdater(
    data_file='satellite_data72_1.csv',  # 可选，有默认值
    config_file='config.yaml',           # 可选，有默认值
    time_manager=time_manager            # 可选，自动创建
)
```

#### 获取卫星数据
```python
# 获取时隙100的所有卫星
satellites = orbital.get_satellites_at_time(100)

# 获取卫星ID列表
sat_ids = orbital.get_satellite_ids(100)

# 获取可见性矩阵和距离矩阵
visibility, distances = orbital.build_visibility_matrix(satellites)
```

### 4.5 使用示例
```python
# 完整示例：计算时隙100的卫星间可见链路
orbital = OrbitalUpdater()
satellites = orbital.get_satellites_at_time(100)
visibility, distances = orbital.build_visibility_matrix(satellites)

# 统计可见链路
num_visible_links = np.sum(visibility)
avg_distance = np.mean(distances[visibility])
print(f"时隙100: {num_visible_links}条可见链路，平均距离{avg_distance:.1f}km")
```

## 五、通信链路管理模块 (communication_refactored.py)

### 5.1 模块作用
基于轨道模块提供的距离数据，使用物理模型计算所有通信链路的质量指标，包括路径损耗、信噪比和数据速率。

### 5.2 物理模型

#### 自由空间路径损耗 (FSPL)
```
FSPL(dB) = 20·log10(d) + 20·log10(f) + 20·log10(4π/c)
```

#### 接收功率
```
Pr(dBm) = Pt(dBm) + Gt(dB) + Gr(dB) - FSPL(dB) - L_other(dB)
```

#### Shannon容量
```
C = B · log2(1 + SNR)
```

### 5.3 链路类型

| 链路类型 | 技术 | 数据速率 | 功率配置 | 带宽 |
|---------|------|---------|---------|------|
| ISL星间链路 | 激光 | 50 Gbps | - | - |
| 用户→卫星 | RF | ~45-50 Mbps | 5W | 100 MHz |
| 卫星→用户 | RF | ~125-130 Mbps | 10W | 120 MHz |
| 云→卫星 | RF | ~320 Mbps | 50W | 180 MHz |
| 卫星→云 | RF | ~135 Mbps | 15W | 150 MHz |

### 5.4 主要接口

#### 初始化
```python
from env.physics_layer.communication_refactored import CommunicationManager

comm = CommunicationManager(
    orbital_updater=orbital,  # 可选，自动创建
    config_file='config.yaml' # 可选，有默认值
)
```

#### 获取通信矩阵
```python
# ISL星间链路
isl_comm = comm.get_isl_communication_matrix(time_step)

# 卫星-地面链路
sat_ground_comm = comm.get_satellite_ground_communication_matrix(time_step)

# 卫星-云链路
sat_cloud_comm = comm.get_satellite_cloud_communication_matrix(time_step)

# 获取汇总指标
metrics = comm.get_link_quality_metrics(time_step)
```

### 5.5 使用示例
```python
# 完整示例：分析时隙100的通信链路
comm = CommunicationManager()

# 获取ISL链路数据
isl = comm.get_isl_communication_matrix(100)
print(f"ISL链路数: {np.sum(isl['visibility'])}")
print(f"平均数据率: {np.mean(isl['data_rate_bps'])/1e9:.1f} Gbps")

# 获取卫星-地面链路
ground = comm.get_satellite_ground_communication_matrix(100)
visible = ground['visibility']
print(f"地面可见链路: {np.sum(visible)}")
print(f"平均上行速率: {np.mean(ground['uplink_data_rate_bps'][visible])/1e6:.1f} Mbps")
```

### 5.6 输出数据格式

#### ISL通信矩阵返回格式
```python
{
    'data_rate_bps': np.ndarray,      # 数据速率矩阵 (bps)
    'snr_db': np.ndarray,              # 信噪比矩阵 (dB)
    'distance_km': np.ndarray,         # 距离矩阵 (km)
    'visibility': np.ndarray,          # 可见性布尔矩阵
    'propagation_delay_ms': np.ndarray,# 传播延迟 (ms)
    'link_type': 'laser',              # 链路类型
    'satellite_ids': list              # 卫星ID列表
}
```

#### 地面/云链路返回格式
```python
{
    'uplink_data_rate_bps': np.ndarray,   # 上行速率
    'uplink_snr_db': np.ndarray,          # 上行SNR
    'downlink_data_rate_bps': np.ndarray, # 下行速率
    'downlink_snr_db': np.ndarray,        # 下行SNR
    'distance_km': np.ndarray,            # 距离矩阵
    'visibility': np.ndarray,             # 可见性矩阵
    'propagation_delay_ms': np.ndarray,   # 传播延迟
    'satellite_ids': list,                # 卫星ID列表
    'ground_station_ids': list            # 地面站ID列表
}
```

## 六、模块集成指南

### 6.1 初始化顺序
```python
# 1. 加载配置
config = load_config('config.yaml')

# 2. 创建时间管理器
time_manager = create_time_manager_from_config(config)

# 3. 创建轨道更新器
orbital = OrbitalUpdater(config_file='config.yaml', time_manager=time_manager)

# 4. 创建通信管理器
comm = CommunicationManager(orbital_updater=orbital, config_file='config.yaml')

# 5. [待开发] 创建计算管理器
# compute = ComputationManager(config_file='config.yaml')

# 6. [待开发] 创建任务调度器
# scheduler = TaskScheduler(orbital, comm, compute)

# 7. [待开发] 创建环境
# env = SPACE2Environment(scheduler)
```

### 6.2 数据流向
```
配置文件 → TimeManager → OrbitalUpdater → CommunicationManager
                              ↓
                         位置/可见性数据
                              ↓
                    [ComputationManager] ← 链路质量数据
                              ↓
                        [TaskScheduler]
                              ↓
                         [Environment]
```

### 6.3 典型使用场景

#### 场景1：单时隙分析
```python
def analyze_single_timestep(timestep):
    orbital = OrbitalUpdater()
    comm = CommunicationManager(orbital)
    
    # 获取该时隙的所有信息
    satellites = orbital.get_satellites_at_time(timestep)
    isl = comm.get_isl_communication_matrix(timestep)
    metrics = comm.get_link_quality_metrics(timestep)
    
    return {
        'num_satellites': len(satellites),
        'isl_links': np.sum(isl['visibility']),
        'avg_isl_rate_gbps': metrics['isl']['avg_data_rate_gbps']
    }
```

#### 场景2：时序分析
```python
def analyze_time_series(start, end):
    orbital = OrbitalUpdater()
    comm = CommunicationManager(orbital)
    results = []
    
    for t in range(start, end):
        metrics = comm.get_link_quality_metrics(t)
        results.append({
            'timestep': t,
            'total_links': sum(m['total_links'] for m in metrics.values())
        })
    
    return pd.DataFrame(results)
```

## 七、测试验证

### 7.1 已完成的测试

| 测试模块 | 测试文件 | 覆盖范围 |
|---------|---------|---------|
| 轨道动力学 | `test/physics_layer/test_orbital.py` | 位置计算、可见性矩阵、距离计算 |
| 通信链路 | `test/physics_layer/test_communication_detailed.py` | 路径损耗、SNR、数据速率、全时隙测试 |

### 7.2 测试数据说明

#### 生成的测试文件
- `communication_summary_stats.csv`: 17个时隙的汇总统计
- `communication_isl_links.csv`: 3654条ISL链路详细数据
- `communication_sat_ground_links.csv`: 13706条地面链路数据
- `communication_sat_cloud_links.csv`: 302条云链路数据

### 7.3 性能指标

| 操作 | 执行时间 | 备注 |
|-----|---------|------|
| 加载卫星数据 | ~60ms | 103,752条记录 |
| 计算可见性矩阵 | ~2ms | 72×72矩阵，使用缓存<1ms |
| 获取通信矩阵 | ~3ms | 包含所有计算 |
| 全时隙测试(17个) | ~2s | 包含文件I/O |

## 八、环境数据文件 (env_data)

### 8.1 数据目录概述
- **位置**: `src/env/env_data/`
- **作用**: 存储仿真环境所需的基础数据文件
- **包含文件**: 4个核心数据文件，支撑整个仿真系统运行

### 8.2 卫星轨道数据 (satellite_data72_1.csv)

#### 8.2.1 文件基本信息
- **文件路径**: `src/env/env_data/satellite_data72_1.csv`
- **文件大小**: 103,754行数据
- **数据范围**: 72颗卫星 × 1441个时隙的完整轨道数据
- **时间跨度**: 7205秒 (约2小时)

#### 8.2.2 数据格式
```csv
satellite_ID,time_slot,time,lat,lon,light,state
111,1,04:00:00,0.0,-16.922,FALSE,TRUE
112,1,04:00:00,33.483,15.261,TRUE,TRUE
113,1,04:00:00,51.158,73.078,TRUE,TRUE
```

#### 8.2.3 字段说明
| 字段名 | 类型 | 说明 | 示例值 |
|--------|------|------|--------|
| satellite_ID | int | 卫星唯一标识符 | 111-182 |
| time_slot | int | 时隙编号 | 1-1441 |
| time | string | 物理时间戳 | 04:00:00 |
| lat | float | 纬度坐标 (度) | -90.0 ~ 90.0 |
| lon | float | 经度坐标 (度) | -180.0 ~ 180.0 |
| light | boolean | 是否处于阳光照射 | TRUE/FALSE |
| state | boolean | 卫星工作状态 | TRUE/FALSE |

### 8.3 云计算中心数据 (cloud_station.csv)

#### 8.3.1 文件基本信息
- **文件路径**: `src/env/env_data/cloud_station.csv`
- **记录数**: 5个云计算中心
- **作用**: 定义云计算中心的地理位置

#### 8.3.2 数据格式
```csv
ID,Latitude,Longitude
1,35,-108
2,55,12
3,35,84
4,35,132
5,-25,120
```

#### 8.3.3 云中心分布
| ID | 位置描述 | 纬度 | 经度 | 覆盖区域 |
|----|---------|------|------|---------|
| 1 | 北美西部 | 35° | -108° | 美国西南部 |
| 2 | 北欧 | 55° | 12° | 斯堪的纳维亚 |
| 3 | 中亚 | 35° | 84° | 中国西部 |
| 4 | 东亚 | 35° | 132° | 日本/韩国 |
| 5 | 澳洲 | -25° | 120° | 澳大利亚西部 |

### 8.4 地面用户终端数据 (global_ground_stations.csv)

#### 8.4.1 文件基本信息
- **文件路径**: `src/env/env_data/global_ground_stations.csv`
- **记录数**: 421行 (包含表头，420个用户终端)
- **覆盖范围**: 全球分布的地面用户终端

#### 8.4.2 数据格式
```csv
ID,Latitude,Longitude,RegionType,Size,PurposeType
1,-65,-180,Land,Medium,Normal
2,-65,-168,Land,Large,Normal
3,-65,-156,Land,Small,DelaySensitive
```

#### 8.4.3 字段说明
| 字段名 | 类型 | 说明 | 可能值 |
|--------|------|------|--------|
| ID | int | 用户终端唯一标识 | 1-420 |
| Latitude | float | 纬度坐标 | -65° ~ 65° |
| Longitude | float | 经度坐标 | -180° ~ 180° |
| RegionType | string | 地理类型 | Land, Ocean |
| Size | string | 终端规模 | Small, Medium, Large |
| PurposeType | string | 功能类型 | Normal, DelaySensitive, Industrial |

#### 8.4.4 分布特征
- **纬度范围**: -65°到65°，避开极地区域
- **经度间隔**: 12°均匀分布
- **地理分布**:
  - Land: 陆地区域终端
  - Ocean: 海洋区域终端（如海上平台、船舶）
- **规模分布**:
  - Small: 小型终端（个人/小企业）
  - Medium: 中型终端（企业/机构）
  - Large: 大型终端（数据中心/大企业）
- **功能分类**:
  - Normal: 普通业务需求
  - DelaySensitive: 延迟敏感业务
  - Industrial: 工业应用

### 8.5 任务生成数据 (task_results.json)

#### 8.5.1 文件基本信息
- **文件路径**: `src/env/env_data/task_results.json`
- **数据类型**: JSON格式
- **内容**: 完整的任务生成仿真结果
- **任务总数**: 2,562,232个任务

#### 8.5.2 数据结构
```json
{
  "simulation_metadata": {
    "total_locations": 420,
    "total_timeslots": 1441,
    "timeslot_duration_seconds": 5,
    "land_base_rate": 10.0,
    "total_tasks_generated": 2562232,
    "generation_timestamp": "2025-08-09T19:31:52.748418"
  },
  "simulation_results": [...]
}
```

#### 8.5.3 元数据说明
| 字段名 | 值 | 说明 |
|--------|---|------|
| total_locations | 420 | 地面用户终端总数 |
| total_timeslots | 1441 | 仿真时隙总数 |
| timeslot_duration_seconds | 5 | 每个时隙持续时间(秒) |
| land_base_rate | 10.0 | 陆地基础任务生成率 |
| total_tasks_generated | 2,562,232 | 生成的任务总数 |
| generation_timestamp | 2025-08-09T19:31:52 | 数据生成时间戳 |

#### 8.5.4 任务数据结构
每个时隙的每个位置包含以下信息：
```json
{
  "location_id": 1,
  "coordinates": [-65.0, -180.0],
  "geography": "Ocean",
  "scale": "Small",
  "functional_type": "Normal",
  "lambda_i": 1.0,
  "num_tasks": 1,
  "generated_tasks": [
    {
      "task_id": 1,
      "type_id": 2,
      "data_size_mb": 37.96,
      "complexity_cycles_per_bit": 200,
      "deadline_timestamp": 50,
      "priority": 2
    }
  ]
}
```

#### 8.5.5 任务类型定义
| type_id | 任务类型 | 复杂度 | 截止时间 | 应用场景 |
|---------|---------|--------|---------|---------|
| 1 | 实时任务 | 100 cycles/bit | 10-25 时隙 | 视频通话、游戏 |
| 2 | 普通任务 | 200 cycles/bit | 50-65 时隙 | 文件传输、网页 |
| 3 | 计算密集 | 300 cycles/bit | 100-115 时隙 | AI推理、数据分析 |

#### 8.5.6 优先级系统
- **优先级范围**: 1-5 (1最高，5最低)
- **调度影响**: 高优先级任务优先分配资源
- **QoS保证**: 不同优先级对应不同服务质量要求

### 8.6 数据文件使用指南

#### 8.6.1 数据加载示例
```python
import pandas as pd
import json

# 加载卫星轨道数据
satellite_data = pd.read_csv('src/env/env_data/satellite_data72_1.csv')

# 加载地面终端数据
ground_stations = pd.read_csv('src/env/env_data/global_ground_stations.csv')

# 加载云中心数据
cloud_stations = pd.read_csv('src/env/env_data/cloud_station.csv')

# 加载任务数据
with open('src/env/env_data/task_results.json', 'r') as f:
    task_data = json.load(f)
```

#### 8.6.2 数据查询示例
```python
# 获取特定时隙的卫星位置
def get_satellites_at_timeslot(timeslot):
    return satellite_data[satellite_data['time_slot'] == timeslot]

# 获取特定区域的地面终端
def get_stations_in_region(lat_min, lat_max, lon_min, lon_max):
    return ground_stations[
        (ground_stations['Latitude'] >= lat_min) &
        (ground_stations['Latitude'] <= lat_max) &
        (ground_stations['Longitude'] >= lon_min) &
        (ground_stations['Longitude'] <= lon_max)
    ]

# 获取特定时隙的任务
def get_tasks_at_timeslot(timeslot):
    return task_data['simulation_results'][timeslot]
```

#### 8.6.3 数据完整性验证
- **卫星数据**: 72卫星 × 1441时隙 = 103,752条记录 ✓
- **地面终端**: 420个终端，全球均匀分布 ✓
- **云中心**: 5个中心，覆盖主要大陆 ✓
- **任务数据**: 2,562,232个任务，符合泊松分布 ✓

## 九、任务管理模块

### 9.1 任务生成器 (task_generator.py)

#### 9.1.1 模块作用
- 基于地理位置和功能类型生成任务
- 实现泊松分布的任务到达模型
- 支持三种任务类型和五级优先级系统
- 支持批量模拟生成和实时生成两种模式

#### 9.1.2 核心类结构

##### Location类
```python
class Location:
    location_id: int         # 位置唯一ID
    latitude: float         # 纬度
    longitude: float        # 经度
    geography: str          # Land/Ocean
    scale: str             # Small/Medium/Large
    functional_type: str    # Normal/Industrial/DelaySensitive
    coordinates: Tuple      # (lat, lon)元组
```

##### Task类
```python
class Task:
    task_id: int                      # 任务唯一ID
    type_id: int                      # 任务类型(1/2/3)
    data_size_mb: float               # 数据大小(MB)
    complexity_cycles_per_bit: int    # 计算复杂度
    deadline_timestamp: float         # 截止时间戳
    priority: int                     # 优先级(1-5)
```

##### TaskGenerator类
```python
class TaskGenerator:
    task_id_counter: int              # 任务ID计数器
    current_time: float               # 当前仿真时间
    locations: List[Location]        # 位置列表
    
    # 主要方法
    load_locations_from_csv()         # 加载地面站数据
    calculate_lambda()                # 计算任务生成率
    sample_task_type()                # 采样任务类型
    generate_task_parameters()        # 生成任务参数
    generate_tasks_for_location()     # 为单个位置生成任务
    run_simulation()                  # 运行批量仿真
```

#### 9.1.3 任务生成模型

##### 任务到达率 (λ)
| 地理类型 | 规模 | 任务生成率 (λ) |
|---------|------|----------------|
| Ocean | - | 1.0 |
| Land | Small | 3.0 |
| Land | Medium | 6.0 |
| Land | Large | 10.0 |

##### 任务类型分布
| 功能类型 | Type 1 | Type 2 | Type 3 |
|---------|--------|--------|--------|
| Normal | 20% | 60% | 20% |
| Industrial | 20% | 20% | 60% |
| DelaySensitive | 60% | 20% | 20% |

##### 任务参数
| 任务类型 | 数据大小 (MB) | 复杂度 (cycles/bit) | 截止时间 (秒) |
|---------|--------------|---------------------|--------------|
| Type 1 | 10-20 | 100 | 5 |
| Type 2 | 20-50 | 200 | 15 |
| Type 3 | 50-150 | 300 | 30 |

#### 9.1.4 数据文件路径
- **输入文件**: `src/env/physics_layer/updated_global_ground_stations.csv`
- **输出文件**: `task_generation_results.json` (批量模拟时生成)

#### 9.1.5 使用示例

##### 批量仿真模式
```python
from src.env.physics_layer.task_generator import TaskGenerator

# 创建生成器
generator = TaskGenerator()

# 加载地面站位置
generator.load_locations_from_csv('updated_global_ground_stations.csv')

# 运行完整仿真(1441个时隙)
results = generator.run_simulation()

# 保存结果
generator.save_results_to_file(results, 'task_generation_results.json')
```

##### 实时生成模式
```python
from src.env.physics_layer.task_generator import TaskGenerator

# 创建生成器
generator = TaskGenerator()

# 加载地面站位置
generator.load_locations_from_csv('updated_global_ground_stations.csv')

# 为特定位置和时间生成任务
location = generator.locations[0]
current_time = 100  # 秒
tasks = generator.generate_tasks_for_location(location, current_time)

print(f"位置 {location.location_id} 生成了 {len(tasks)} 个任务")
```

#### 9.1.6 关键特性
- **泊松分布**: 使用`np.random.poisson(lambda_i)`实现真实的任务到达模型
- **任务类型采样**: 根据功能类型(Normal/Industrial/DelaySensitive)使用不同概率分布
- **优先级系统**: 均匀分布生成1-5级优先级
- **可重现性**: 支持设置随机种子(seed=42)确保结果可重复

### 9.2 任务跟踪系统 (task_tracking.py)

#### 9.2.1 模块作用
- 管理任务完整生命周期（从生成到交付）
- 实现重传机制（最多3次尝试，指数退避）
- 跟踪任务状态和性能指标
- 集成真实卫星可见性进行任务分配
- 支持多节点协同处理（卫星、云、地面）

#### 9.2.2 核心类结构

##### TaskStatus枚举
```python
class TaskStatus(Enum):
    GENERATED = "generated"              # 任务生成
    WAITING_ACCESS = "waiting_access"    # 等待接入
    RETRANSMITTING = "retransmitting"    # 重传中
    ASSIGNED = "assigned"                # 已分配
    ROUTING = "routing"                  # 路由中
    LOCAL_PROCESSING = "local_processing"           # 本地处理
    SATELLITE_OFFLOADING = "satellite_offloading"   # 卫星卸载
    CLOUD_OFFLOADING = "cloud_offloading"          # 云端卸载
    PROCESSING = "processing"            # 处理中
    AGGREGATING = "aggregating"          # 聚合中
    RETURNING = "returning"              # 返回中
    DELIVERED = "delivered"              # 已交付
    FAILED = "failed"                   # 失败
    TIMEOUT = "timeout"                  # 超时
```

##### ProcessingNode数据类
```python
@dataclass
class ProcessingNode:
    node_type: NodeType                 # 节点类型(SATELLITE/CLOUD/GROUND)
    node_id: int                        # 节点ID
    processing_percentage: float        # 处理百分比
    start_time: float                   # 开始时间
    end_time: float                     # 结束时间
    
    # 性能指标
    processing_time: float              # 处理时间
    energy_consumption: float           # 能耗
    cpu_cycles_used: float             # CPU周期
    memory_usage_mb: float             # 内存使用
    
    # 传输指标
    offload_transmission_time: float   # 卸载传输时间
    offload_bandwidth_usage: float     # 卸载带宽使用
    result_transmission_time: float    # 结果传输时间
    result_size_mb: float              # 结果大小
```

##### TaskTrackingRecord数据类
```python
@dataclass
class TaskTrackingRecord:
    # 基本信息
    task_id: int                       # 任务ID
    source_location_id: int            # 源位置ID
    generation_time: int               # 生成时间
    task_type: int                     # 任务类型
    data_size_mb: float               # 数据大小
    complexity_cycles_per_bit: int    # 计算复杂度
    deadline_timestamp: int           # 截止时间
    priority: int                     # 优先级
    coordinates: Tuple[float, float]  # 坐标
    
    # 处理信息
    access_satellite_id: Optional[int]     # 接入卫星ID
    processing_nodes: List[ProcessingNode] # 处理节点列表
    
    # 时间统计
    total_processing_time: float      # 总处理时间
    total_transmission_time: float    # 总传输时间
    total_queuing_time: float        # 总排队时间
    delivery_time: Optional[int]     # 交付时间
    
    # 资源统计
    total_energy_consumption: float   # 总能耗
    total_bandwidth_usage: float     # 总带宽使用
    total_computation_used: float    # 总计算使用
    
    # 重传信息
    transmission_attempts: int        # 传输尝试次数
    retransmission_count: int        # 重传次数
    last_transmission_time: Optional[int]  # 最后传输时间
    retransmission_intervals: List[int]    # 重传间隔列表
    
    # 状态信息
    current_status: TaskStatus       # 当前状态
    completion_percentage: float     # 完成百分比
    is_delivered: bool              # 是否已交付
    meet_deadline: bool             # 是否满足截止时间
    failure_reason: Optional[FailureReason]  # 失败原因
```

#### 9.2.3 任务状态机

```
GENERATED → WAITING_ACCESS → ASSIGNED → PROCESSING → DELIVERED
                ↓                           ↓
         RETRANSMITTING                  FAILED/TIMEOUT
```

##### 状态说明
| 状态 | 说明 |
|------|------|
| GENERATED | 任务刚生成 |
| WAITING_ACCESS | 等待卫星接入 |
| RETRANSMITTING | 重传中 |
| ASSIGNED | 已分配给卫星 |
| PROCESSING | 处理中 |
| DELIVERED | 已交付 |
| FAILED | 失败（超过重传次数） |
| TIMEOUT | 超时（超过截止时间） |

#### 9.2.3 重传策略
```python
class RetransmissionPolicy:
    MAX_ATTEMPTS = 3           # 最大尝试次数
    RETRANSMIT_INTERVAL = 5     # 基础重传间隔（秒）
    BACKOFF_FACTOR = 1.5        # 指数退避因子
    
    # 重传间隔计算：interval = base * (factor ^ (attempt - 1))
```

#### 9.2.4 关键组件

##### TaskDataLoader类
```python
class TaskDataLoader:
    """任务数据加载器 - 使用TaskGenerator实时生成"""
    
    def __init__(self, seed: int = 42):
        self.generator = TaskGenerator()  # 内置任务生成器
        self.cached_timeslots = {}       # 缓存已生成的时隙数据
        self.total_task_counter = 0      # 任务计数器
    
    def get_tasks_at_timeslot(timeslot: int) -> List[Task]:
        """获取特定时隙的任务(实时生成或从缓存获取)"""
```

##### TaskTrackingSystem类
```python
class TaskTrackingSystem:
    """任务跟踪系统主类"""
    
    def __init__(self):
        self.data_loader = TaskDataLoader()           # 任务数据加载器
        self.orbital_updater = OrbitalUpdater()       # 轨道更新器(自动初始化)
        self.retransmission_policy = RetransmissionPolicy()  # 重传策略
        self.task_records: Dict[int, TaskTrackingRecord] = {}  # 任务记录
        
        # 队列管理
        self.waiting_queue: List[int] = []            # 等待队列
        self.retransmit_queue: Dict[int, int] = {}   # 重传队列
        
    # 核心方法
    def register_new_tasks(time_step: int)           # 注册新任务
    def attempt_task_access(task_id: int, current_time: int) -> bool  # 尝试接入
    def process_retransmissions(current_time: int)   # 处理重传
    def simulate_step(time_step: int)                # 仿真步骤
```

#### 9.2.5 任务分配算法

##### 卫星选择策略
- **基于真实轨道数据**：使用OrbitalUpdater获取卫星可见性
- **最近优先**：任务分配给距离最近的可见卫星
- **动态分配**：每个时隙重新计算可见性和分配

##### 具体实现
```python
def attempt_task_access(self, task_id: int, current_time: int) -> bool:
    # 1. 获取当前时隙的卫星位置
    satellites = self.orbital_updater.get_satellites_at_time(current_time)
    
    # 2. 构建卫星-地面站可见性矩阵和距离矩阵
    visibility_matrix, distance_matrix = 
        self.orbital_updater.build_satellite_ground_visibility_matrix(
            satellites, current_time)
    
    # 3. 获取该地面站可见的卫星
    ground_index = record.source_location_id - 1
    visible_satellite_indices = np.where(visibility_matrix[:, ground_index])[0]
    
    # 4. 找到距离最近的卫星
    distances_to_visible = distance_matrix[visible_satellite_indices, ground_index]
    closest_idx = visible_satellite_indices[np.argmin(distances_to_visible)]
    
    # 5. 分配任务给最近的卫星
    record.access_satellite_id = selected_satellite
    record.current_status = TaskStatus.ASSIGNED
```

#### 9.2.6 核心接口
```python
from src.env.physics_layer.task_tracking import TaskTrackingSystem

# 创建任务跟踪系统
tracker = TaskTrackingSystem()

# 仿真一个时隙
metrics = tracker.simulate_step(time_step=0)

# 获取任务记录
record = tracker.get_task_record(task_id=1)

# 获取统计信息
stats = tracker.get_statistics()
print(f"完成率: {stats['completion_rate']:.2%}")
print(f"平均传输尝试: {stats['avg_transmission_attempts']:.2f}")
```

#### 9.2.7 性能指标跟踪

##### 实时指标 (metrics)
| 指标 | 说明 |
|------|------|
| active_tasks | 活跃任务数 |
| processing_tasks | 处理中任务数 |
| queued_tasks | 排队任务数 |
| retransmitting_tasks | 重传任务数 |
| completed_tasks | 完成任务数 |
| failed_tasks | 失败任务数 |
| timeout_tasks | 超时任务数 |
| retry_exhausted_tasks | 重试耗尽任务数 |

##### 统计指标 (statistics)
| 指标 | 说明 |
|------|------|
| total_tasks | 总任务数 |
| completion_rate | 任务完成率 |
| failure_rate | 失败率 |
| avg_transmission_attempts | 平均传输尝试次数 |
| retransmission_rate | 重传率 |
| first_attempt_success_rate | 首次成功率 |
| failure_breakdown | 失败原因分布 |

### 9.3 模块集成特性

#### 9.3.1 任务生成器与跟踪系统集成
- **TaskDataLoader内置TaskGenerator**：跟踪系统通过TaskDataLoader自动使用TaskGenerator
- **实时生成模式**：不需要预先生成所有任务，按需生成节省内存
- **缓存机制**：最多缓存100个时隙的任务数据，平衡内存和性能
- **种子管理**：支持设置随机种子确保仿真可重复性

#### 9.3.2 与轨道模块集成
```python
# 自动初始化轨道更新器
if not hasattr(self, 'orbital_updater'):
    self.orbital_updater = OrbitalUpdater()

# 获取实时卫星可见性
satellites = orbital_updater.get_satellites_at_time(time_step)
visibility_matrix, distance_matrix = orbital_updater.build_satellite_ground_visibility_matrix(
    satellites, time_step
)

# 选择最近的可见卫星
visible_satellites = np.where(visibility_matrix[:, ground_index])[0]
closest_satellite = visible_satellites[np.argmin(distances_to_visible)]
```

#### 9.3.3 任务生成统计特征
- **任务类型分布**：
  - Type 1 (实时): ~33%
  - Type 2 (普通): ~33%
  - Type 3 (计算密集): ~34%
- **平均生成率**：~1780任务/时隙（全系统420个位置）
- **地理分布**：
  - 陆地大型站点: 10任务/时隙
  - 陆地中型站点: 6任务/时隙
  - 陆地小型站点: 3任务/时隙
  - 海洋站点: 1任务/时隙
- **卫星负载分布**：72颗卫星均参与任务分配

#### 9.3.4 接口预留
系统预留了与其他模块的接口，便于后续扩展：

##### SatelliteInterface (卫星资源管理接口)
```python
class SatelliteInterface:
    get_visible_satellites()    # 获取可见卫星列表
    get_satellite_status()      # 获取卫星状态
    allocate_resources()        # 分配卫星资源
    release_resources()         # 释放卫星资源
    process_task()             # 在卫星上处理任务
```

##### CloudInterface (云端处理接口)
```python
class CloudInterface:
    get_available_clouds()      # 获取可用云中心
    get_cloud_status()         # 获取云中心状态
    offload_to_cloud()         # 卸载到云端
    process_task()             # 在云端处理任务
```

##### CommunicationInterface (通信管理接口)
```python
class CommunicationInterface:
    get_link_quality()         # 获取链路质量
    transmit_data()           # 传输数据
```

### 9.4 测试验证

#### 9.4.1 测试覆盖
| 测试项 | 测试文件 | 验证内容 |
|--------|---------|---------|
| 任务生成 | 内置于task_generator.py | 泊松分布、参数生成、批量仿真 |
| 任务跟踪 | test/physics_layer/test_task_tracking.py | 生命周期、重传机制、卫星分配 |
| 集成测试 | 两个模块的main函数 | 端到端流程验证 |

#### 9.4.2 运行测试

##### 测试任务生成器
```bash
python src/env/physics_layer/task_generator.py
# 输出: 生成task_generation_results.json文件
```

##### 测试任务跟踪系统
```bash
python src/env/physics_layer/task_tracking.py
# 输出: 5个时隙的仿真结果和统计信息
```

### 9.5 使用注意事项

#### 9.5.1 系统配置
1. **任务ID唯一性**：系统自动分配递增ID，确保全局唯一
2. **时隙同步**：
   - 任务生成时间与时隙严格对应（时隙×5秒）
   - 时隙编号从0开始，对应物理时间04:00:00
3. **随机种子**：默认seed=42，可修改以生成不同的任务序列

#### 9.5.2 性能优化
1. **内存管理**：
   - TaskDataLoader缓存限制为100个时隙
   - 长时间运行需定期清理已完成任务记录
2. **计算优化**：
   - 卫星可见性计算使用缓存机制
   - 矩阵运算使用NumPy向量化
3. **数据持久化**：
   - 支持将任务生成结果保存为JSON文件
   - 可从文件加载历史任务数据

#### 9.5.3 扩展建议
1. **任务处理逻辑**：当前仅实现任务分配，处理逻辑待扩展
2. **多跳卸载**：预留了卫星间、卫星-云的卸载接口
3. **资源管理**：需要实现计算、存储、带宽资源的动态管理
4. **调度算法**：可扩展更复杂的任务调度策略

## 十、待开发模块



## 附录

### A. API快速参考

```python
# 时间管理
time_manager = create_time_manager_from_config(config)
context = time_manager.get_time_context(step)

# 轨道动力学
orbital = OrbitalUpdater()
satellites = orbital.get_satellites_at_time(step)
vis, dist = orbital.build_visibility_matrix(satellites)

# 通信管理
comm = CommunicationManager()
isl = comm.get_isl_communication_matrix(step)
ground = comm.get_satellite_ground_communication_matrix(step)
cloud = comm.get_satellite_cloud_communication_matrix(step)

# 任务生成
generator = TaskGenerator()
generator.load_locations_from_csv('updated_global_ground_stations.csv')
tasks = generator.generate_tasks_for_location(location, current_time)

# 任务跟踪
tracker = TaskTrackingSystem()
metrics = tracker.simulate_step(time_step)
stats = tracker.get_statistics()
```

### B. 常见问题

**Q1: 为什么部分链路数据速率为0？**
- A: 当SNR < 0 dB时，链路质量太差无法通信，速率设为0。这通常发生在接近可见性边界的远距离链路。

**Q2: 如何调整可见性阈值？**
- A: 修改`config.yaml`中的`visibility_threshold_m`、`visibility_earth_m`、`cloud_visibility_threshold_m`参数。

**Q3: 缓存如何工作？**
- A: 轨道和通信模块都实现了缓存机制，相同时隙的重复查询会直接返回缓存结果。使用`clear_cache()`方法清除缓存。

**Q4: 任务生成如何保证可重复性？**
- A: TaskGenerator和TaskDataLoader都支持设置随机种子(seed)，相同种子会生成相同的任务序列。

**Q5: 如何处理任务超时？**
- A: TaskTrackingSystem会在每个时隙检查超时任务，自动将超过deadline的任务标记为TIMEOUT状态。

### C. 性能优化建议

1. **使用向量化操作**: 所有矩阵计算都使用NumPy向量化，避免Python循环
2. **启用缓存**: 对于重复查询的时隙，缓存可提供>1000倍加速
4. **预计算**: 可预先计算并存储所有时隙的可见性矩阵

---
