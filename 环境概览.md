基于提供的文档，这是一个**LEO卫星星座边缘-云协同计算仿真环境**（SPACE2），主要任务是模拟72颗LEO卫星、420个地面用户终端和5个云计算中心在1441个时隙（7205秒）内的复杂交互，支持多智能体强化学习算法研究。

## 环境核心任务
该环境旨在研究卫星边缘计算中的任务卸载、资源分配和调度策略。系统模拟真实的卫星轨道动力学、通信链路质量变化、任务生成与处理等复杂场景，为强化学习算法提供训练平台。

## 已完成程序模块

### 基础设施层
- **`config.yaml`**: 集中配置管理，定义系统参数（卫星数量、轨道高度、通信参数等）
- **`time_manager.py`**: 时间管理，处理仿真时间与物理时间映射
- **`error_handler.py`**: 统一错误处理框架
- **`logger_config.py`**: 日志管理系统

### 物理仿真层
- **`orbital.py`**: 轨道动力学模块，管理卫星位置计算和可见性矩阵（卫星间、卫星-地面、卫星-云三种）
- **`communication_refactored.py`**: 通信链路管理，基于物理模型计算路径损耗、信噪比和数据速率，支持5种链路类型（ISL星间、上下行地面、上下行云）

### 任务管理层
- **`task_generator.py`**: 任务生成器，基于泊松分布模型生成三种类型任务（实时、普通、计算密集），支持三级优先级
- **`task_tracking.py`**: 任务跟踪系统，管理任务完整生命周期，包含重传机制和基于距离的最优卫星选择

### 环境数据
- **`satellite_data72_1.csv`**: 72个卫星，1441个时隙的轨道数据
- **`cloud_station.csv`**: 5个云计算中心位置
- **`global_ground_stations.csv`**: 420个地面用户终端

## 计算单元
- `satellite.py` : 是单个卫星的任务处理核心模块，实现了基于动态优先级评分的DPSQ调度算法。负责接收任务跟踪系统分配的任务，进行智能排队调度，管理CPU/带宽资源分配，执行任务处理，计算能耗和延迟，处理超时任务丢弃。支持多任务并行处理，提供完整的任务生命周期管理（等待→处理→完成/丢弃），是连接任务分配与实际执行的关键桥梁。
- `cloud.py`: 云服务中心，位于地面，具有强大的计算资源，当卫星计算能力不足时，会将任务交给云服务中心完成。云服务中心完成任务后再将任务传送回卫星


## 待开发模块

- PettingZoo环境封装（`environment.py`）
