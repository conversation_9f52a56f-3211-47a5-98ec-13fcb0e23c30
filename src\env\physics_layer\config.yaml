# ============================================================================
# SPACE-OAAL 仿真环境配置文件
# ============================================================================

# ============================================================================
# 系统架构参数 (System Architecture Parameters)
# ============================================================================
system:
  # 基础系统配置
  num_users: 420                    # 地面用户终端数量
  num_leo_satellites: 72            # LEO卫星数量
  num_policy_domains: 24            # 策略域数量

  # 轨道参数
  leo_altitude_m: 1200000           # LEO卫星轨道高度 (m)
  earth_radius_m: 6371000           # 地球半径 (m)
  
  # 时间参数
  timeslot_duration_s: 5           # 时隙持续时间 (秒)
  total_timeslots: 1500             # 总时隙数
  # 可见距离
  visibility_threshold_m: 5500000    # 卫星间可见性阈值距离 (m)
  visibility_earth_m: 2500000       # 地面站-卫星可见性阈值距离 (m)
  cloud_visibility_threshold_m: 3300000  # 云中心-卫星可见性阈值距离 (m)

# ============================================================================
# 通信模型参数 (Communication Model Parameters)
# ============================================================================
communication:
  # 射频通信参数
  rf_noise_power_w: 1.0e-13         # 射频信道背景噪声 (W)
  rf_carrier_freq_hz: 14000000000    # 射频载波频率 (Hz)
  rician_k_factor: 10               # 莱斯K因子

  # 用户-卫星上行链路
  b_us_hz: 1000000000                # 用户-卫星上行带宽 (Hz)
  p_u_w: 5                          # 用户终端发射功率 (W)

  # 卫星-用户下行链路  
  b_su_hz: 120000000                # 卫星-用户下行带宽 (Hz)
  p_su_w: 10                        # 卫星对用户发射功率 (W)

  # 卫星-云中心下行链路
  b_sc_hz: 150000000                # 卫星-云下行带宽 (Hz)
  p_sc_w: 15                        # 卫星对云发射功率 (W)

  # 云中心-卫星上行链路
  b_cs_hz: 180000000                # 云-卫星上行带宽 (Hz) 
  p_c_w: 50                         # 云中心发射功率 (W)

  # 星间激光链路参数
  isl_tra_rate: 50000000000         # 星间链路传输速率 (bps)

  # 物理常数
  light_speed_ms: 299792458         # 光速 (m/s)

  # 单位转换
  mb_to_bits: 8388608               # MB到bits转换系数
  
  # 通信链路参数
  antenna_gain_db: 33.0             # 天线增益 (dB)
  system_noise_dbm_hz: -174.0       # 系统噪声功率密度 (dBm/Hz)
  implementation_loss_db: 2.0       # 实现损耗 (dB)
  rain_fade_margin_db: 6.0          # 雨衰余量 (dB)
  coding_efficiency: 0.7            # 编码效率
  
  # 协议参数
  max_retries: 3                    # 最大重传次数
  timeout_threshold_ms: 1000        # 通信超时阈值 (ms)
  processing_delay_ms: 5.0          # 处理延迟 (ms)

# ============================================================================
# 计算模型参数 (Computation Model Parameters)
# ============================================================================
computation:
  # LEO卫星计算资源
  f_leo_hz: 50e9                   # LEO卫星CPU频率 (Hz) - 提升到100GHz以加快任务处理

  # 云计算资源
  f_cloud_hz: 100e9                 # 云中心CPU频率 (Hz)

  # 能效参数
  zeta_leo: 1.0e-10                 # LEO卫星能效系数 (J/cycle) - 调整到更合理的值
  zeta_cloud: 1.0e-11               # 云中心能效系数 (J/cycle)

  # 电池和能量约束
  leo_battery_capacity_j: 3600000   # LEO卫星电池容量 (J)
  leo_solar_power_w: 5000            # LEO卫星太阳能板功率 (W)
  energy_threshold_ratio: 0.2       # 能量阈值比例

  # 并行处理参数 (Parallel Processing Parameters)
  max_parallel_tasks: 200             # 最大并行任务数
  cpu_allocation_levels: [10, 20, 30, 40, 50, 60, 70, 80, 90, 100]  # CPU分配档位(%)
  min_cpu_allocation: 10            # 最小CPU分配比例 (%)
  max_cpu_allocation: 100           # 最大CPU分配比例 (%)
  cpu_allocation_step: 10           # CPU分配步长 (%)
  
  # 卫星任务处理参数 (Satellite Task Processing Parameters)
  default_drop_penalty: 100.0        # 默认任务丢弃惩罚
  penalty_multiplier: 2.0            # 惩罚系数（基于优先级）
  processing_overhead_ratio: 0.05    # 处理开销比例
  idle_power_w: 10.0                # 卫星空闲功耗 (W)

# ============================================================================
# 排队模型参数 (Queuing Model Parameters)
# ============================================================================
queuing:
  # 动态优先级评分权重
  w_priority: 1.0                   # 优先级因子权重
  w_urgency: 2.0                    # 紧迫性因子权重
  w_cost: 0.5                       # 成本因子权重

  # 队列管理参数
  max_queue_size: 100               # 最大队列长度
  queue_timeout_s: 300              # 队列超时时间 (秒)
  epsilon_urgency: 1.0e-6           # 紧迫性计算防零参数

  # 调度参数
  scheduling_interval_s: 1          # 调度间隔 (秒)
  preemption_enabled: true          # 是否启用抢占
  priority_aging_factor: 0.01       # 优先级老化因子


# ============================================================================
# 观测空间归一化参数 (Observation Space Normalization Parameters)
# ============================================================================
observation:
  # 位置信息归一化参数
  max_latitude_deg: 90.0            # 最大纬度（度）
  max_longitude_deg: 180.0          # 最大经度（度）
  max_altitude_km: 2000.0           # 最大高度（公里）
  
  # 任务信息归一化参数
  max_queue_length: 10.0            # 任务队列长度归一化基数
  max_task_priority: 10.0           # 最大任务优先级
  max_task_urgency: 100.0           # 最大任务紧急度
  
  # 通信信息归一化参数
  max_neighbor_count: 10.0          # 最大邻居数量
  max_ground_station_count: 12.0     # 最大可见地面站数量

# ============================================================================
# 混合仿真模型参数 (Hybrid Simulation Model Parameters)
# ============================================================================
hybrid_simulation:
  # 核心开关
  enabled: true                     # 启用混合模式 (启用步内离散迭代)
  
  # 时间模型参数
  micro_iterations: 3               # 微观逻辑轮次数 (对应最大卸载跳数)
  max_offloading_hops: 3           # 最大卸载跳数限制
  
  # 调度模式参数
  batch_scheduling: true           # 启用批量任务调度
  path_planning_enabled: true      # 启用多跳路径规划
  
  # 物理建模参数
  precise_latency_calculation: true  # 启用精确延迟计算
  channel_contention_modeling: true # 启用信道竞争建模
  resource_queuing_modeling: true   # 启用资源排队建模
  
  # 调试和性能参数
  debug_mode: false                # 调试模式 (详细日志)
  performance_profiling: false    # 性能分析
  visualization_enabled: false    # 任务流可视化
  
  # 兼容性参数
  fallback_to_original: true      # 允许回退到原始模式
  traditional_reward_fallback: true  # 传统奖励计算回退
  
  # 新增：任务分割处理配置
  enable_task_splitting: true           # 启用任务分割处理
  max_split_participants: 4             # 最大分割参与者数量
  min_split_ratio: 0.1                  # 最小分割比例
  min_split_complexity: 500000          # 最小分割复杂度(CPU cycles)
  min_split_data_size: 5.0              # 最小分割数据大小(MB)
  min_split_neighbors: 1                # 最小邻居数量要求
  
  # 分割处理性能参数
  coordination_overhead_ms: 10.0        # 协调开销(毫秒)
  split_communication_factor: 1.2       # 分割通信开销因子
  parallel_efficiency_bonus: 0.8        # 并行处理效率奖励
  
  # 阶段五增强：高级配置参数
  advanced_config:
    # 延迟计算调优
    latency_calculation:
      base_contention_delay_ms: 5.0        # 基础竞争延迟 (ms)
      contention_exponential_factor: 1.5   # 竞争延迟指数因子
      max_contention_delay_ms: 100.0       # 最大竞争延迟 (ms)
      processing_rate_mbps: 100.0          # 数据处理速率 (MB/s)
      queuing_delay_per_task_ms: 2.0       # 每个任务的排队延迟 (ms)
    
    # 路径规划调优
    path_planning:
      enable_shortest_path: true           # 启用最短路径算法
      enable_load_balancing: true          # 启用负载均衡路径选择
      enable_energy_aware_routing: true    # 启用能量感知路由
      relay_quality_threshold: 0.5         # 中继卫星质量阈值
      path_validation_strict: false        # 严格路径验证模式
    
    # 奖励机制调优
    reward_tuning:
      enable_hybrid_rewards: true          # 启用混合模式奖励
      task_completion_bonus: 1.0           # 任务完成奖励倍数
      efficiency_reward_weight: 0.5        # 效率奖励权重
      collaboration_reward_weight: 0.3     # 协作奖励权重
      latency_penalty_sensitivity: 0.01    # 延迟惩罚敏感度
    
    # 状态同步调优
    state_sync:
      enable_task_state_sync: true         # 启用任务状态同步
      enable_performance_sync: true        # 启用性能指标同步
      enable_energy_sync: true             # 启用能量状态同步
      consistency_validation: true         # 启用一致性验证
      sync_error_tolerance: 0.01           # 同步误差容忍度