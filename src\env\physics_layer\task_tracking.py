"""
任务跟踪管理系统
负责管理任务从产生到交付的完整生命周期
"""

import json
import os
from pathlib import Path
from typing import Dict, List, Optional, Tuple, Any
from dataclasses import dataclass, field, asdict
from enum import Enum
import numpy as np
from datetime import datetime
import logging
import yaml

# 导入轨道模块 - 使用绝对导入
from src.env.physics_layer.orbital import OrbitalUpdater


class TaskStatus(Enum):
    """任务状态枚举"""
    GENERATED = "generated"
    WAITING_ACCESS = "waiting_access"
    RETRANSMITTING = "retransmitting"
    ASSIGNED = "assigned"
    ROUTING = "routing"
    LOCAL_PROCESSING = "local_processing"
    SATELLITE_OFFLOADING = "satellite_offloading"
    CLOUD_OFFLOADING = "cloud_offloading"
    PROCESSING = "processing"
    AGGREGATING = "aggregating"
    RETURNING = "returning"
    DELIVERED = "delivered"
    FAILED = "failed"
    TIMEOUT = "timeout"


class TaskType(Enum):
    """任务类型枚举"""
    REALTIME = 1  # 实时任务
    NORMAL = 2    # 普通任务
    COMPUTE_INTENSIVE = 3  # 计算密集任务


class NodeType(Enum):
    """处理节点类型"""
    SATELLITE = "satellite"
    CLOUD = "cloud"
    GROUND = "ground"


class FailureReason(Enum):
    """失败原因"""
    TIMEOUT = "TIMEOUT"
    MAX_RETRIES = "MAX_RETRIES"
    RESOURCE_UNAVAILABLE = "RESOURCE_UNAVAILABLE"
    NO_SATELLITE = "NO_SATELLITE"


@dataclass
class Task:
    """原始任务数据"""
    task_id: int
    type_id: int
    data_size_mb: float
    complexity_cycles_per_bit: int
    deadline_timestamp: int
    priority: int
    location_id: int
    coordinates: Tuple[float, float]
    generation_time: int
    geography: str = ""
    scale: str = ""
    functional_type: str = ""


@dataclass
class ProcessingNode:
    """处理节点记录"""
    node_type: NodeType
    node_id: int
    processing_percentage: float
    start_time: float
    end_time: float
    
    # 任务分割记录字段
    task_segment_id: Optional[str] = None  # 任务分片ID (格式: task_id-segment_index)
    segment_ratio: float = 0.0  # 该分片占总任务的比例
    is_partial_processing: bool = False  # 是否为部分处理
    
    processing_time: float = 0.0
    energy_consumption: float = 0.0
    cpu_cycles_used: float = 0.0
    memory_usage_mb: float = 0.0
    
    offload_transmission_time: float = 0.0
    offload_bandwidth_usage: float = 0.0
    result_transmission_time: float = 0.0
    result_size_mb: float = 0.0


@dataclass
class TaskTrackingRecord:
    """任务跟踪记录"""
    task_id: int
    source_location_id: int
    generation_time: int
    task_type: int
    data_size_mb: float
    complexity_cycles_per_bit: int
    deadline_timestamp: int
    priority: int
    coordinates: Tuple[float, float]
    
    access_satellite_id: Optional[int] = None
    processing_nodes: List[ProcessingNode] = field(default_factory=list)
    
    total_processing_time: float = 0.0
    total_transmission_time: float = 0.0
    total_queuing_time: float = 0.0
    delivery_time: Optional[int] = None
    
    total_energy_consumption: float = 0.0
    total_bandwidth_usage: float = 0.0
    total_computation_used: float = 0.0
    
    transmission_attempts: int = 0
    retransmission_count: int = 0
    last_transmission_time: Optional[int] = None
    retransmission_intervals: List[int] = field(default_factory=list)
    
    current_status: TaskStatus = TaskStatus.GENERATED
    completion_percentage: float = 0.0
    is_delivered: bool = False
    meet_deadline: bool = False
    failure_reason: Optional[FailureReason] = None


class RetransmissionPolicy:
    """重传策略"""
    def __init__(self, config: Dict = None):
        """从配置文件初始化重传策略"""
        if config is None:
            # 加载配置文件
            config_path = Path(__file__).parent / "config.yaml"
            with open(config_path, 'r', encoding='utf-8') as f:
                full_config = yaml.safe_load(f)
                config = full_config.get('communication', {})
        
        self.MAX_ATTEMPTS = config.get('max_retries', 3)
        self.RETRANSMIT_INTERVAL = config.get('timeout_threshold_ms', 1000) // 200  # 转换为时隙数
        self.BACKOFF_FACTOR = 1.5  # 从config中未找到，保持固定值
    
    def calculate_next_retransmit_time(self, current_time: int, attempt: int) -> int:
        """计算下次重传时间"""
        interval = self.RETRANSMIT_INTERVAL * (self.BACKOFF_FACTOR ** (attempt - 1))
        return current_time + int(interval)
    
    def should_retransmit(self, task_record: TaskTrackingRecord, current_time: int) -> bool:
        """判断是否需要重传"""
        if task_record.transmission_attempts >= self.MAX_ATTEMPTS:
            return False
        if current_time > task_record.deadline_timestamp:
            return False
        return True


class TaskDataLoader:
    """任务数据加载器 - 使用TaskGenerator实时生成"""
    
    def __init__(self, seed: int = None):
        """
        初始化任务数据加载器，使用TaskGenerator实时生成任务
        
        Args:
            seed: 随机种子，确保可重复性
        """
        # 导入任务生成器 - 使用绝对导入
        from src.env.physics_layer.task_generator import TaskGenerator
        
        # 加载配置
        config_path = Path(__file__).parent / "config.yaml"
        with open(config_path, 'r', encoding='utf-8') as f:
            self.config = yaml.safe_load(f)
        
        # 使用配置中的随机种子或传入的种子
        if seed is None:
            seed = self.config.get('system', {}).get('random_seed', 42)
        
        # 设置随机种子
        self.seed = seed
        np.random.seed(seed)
        
        # 初始化生成器
        self.generator = TaskGenerator()
        
        # 加载真实的地面站位置数据
        csv_file = Path(__file__).parent / "global_ground_stations.csv"
        if not csv_file.exists():
            # 尝试从env_data目录加载
            csv_file = Path(__file__).parent.parent / "env_data" / "global_ground_stations.csv"
        
        if not csv_file.exists():
            raise FileNotFoundError(f"地面站数据文件不存在: {csv_file}")
        
        # 加载位置数据
        self.generator.load_locations_from_csv(str(csv_file))
        
        # 设置元数据 - 从配置文件读取
        system_config = self.config.get('system', {})
        self.metadata = {
            'total_locations': len(self.generator.locations),
            'total_timeslots': system_config.get('total_timeslots', 1500),
            'timeslot_duration_seconds': system_config.get('timeslot_duration_s', 4),
            'land_base_rate': 10.0,  # 这个参数可能来自task_generator的配置
            'total_tasks_generated': 0,  # 将在运行时更新
            'generation_timestamp': datetime.now().isoformat()
        }
        
        # 缓存已生成的时隙数据
        self.cached_timeslots = {}
        self.total_task_counter = 0
    
    def get_tasks_at_timeslot(self, timeslot: int) -> List[Task]:
        """获取特定时隙的任务"""
        # 检查缓存
        if timeslot in self.cached_timeslots:
            return self.cached_timeslots[timeslot]
        
        # 设置随机种子确保可重复性（每个时隙使用不同但固定的种子）
        np.random.seed(self.seed + timeslot * 1000)
        
        # 设置生成器的当前时间
        current_time = timeslot * self.metadata['timeslot_duration_seconds']
        self.generator.current_time = current_time
        
        tasks = []
        
        # 为每个位置生成任务
        for location in self.generator.locations:
            # 使用生成器的真实逻辑生成任务
            generated_tasks = self.generator.generate_tasks_for_location(location, current_time)
            
            # 转换为Task对象
            for gen_task in generated_tasks:
                task = Task(
                    task_id=gen_task.task_id,
                    type_id=gen_task.type_id,
                    data_size_mb=gen_task.data_size_mb,
                    complexity_cycles_per_bit=gen_task.complexity_cycles_per_bit,
                    deadline_timestamp=int(gen_task.deadline_timestamp),
                    priority=gen_task.priority,
                    location_id=location.location_id,
                    coordinates=(location.latitude, location.longitude),
                    generation_time=timeslot,
                    geography=location.geography,
                    scale=location.scale,
                    functional_type=location.functional_type
                )
                tasks.append(task)
                self.total_task_counter += 1
        
        # 更新生成器的任务计数器
        self.generator.task_id_counter = self.total_task_counter
        
        # 缓存结果（限制缓存大小以节省内存）
        if len(self.cached_timeslots) < 100:
            self.cached_timeslots[timeslot] = tasks
        else:
            # 如果缓存已满，删除最早的缓存
            if self.cached_timeslots:
                oldest_key = min(self.cached_timeslots.keys())
                del self.cached_timeslots[oldest_key]
            self.cached_timeslots[timeslot] = tasks
        
        return tasks
    
    def get_total_tasks(self) -> int:
        """获取已生成的任务总数"""
        return self.total_task_counter
    
    def get_total_timeslots(self) -> int:
        """获取总时隙数"""
        return self.metadata['total_timeslots']


# 接口定义：与卫星模块的接口
class SatelliteInterface:
    """卫星接口"""
    
    def get_visible_satellites(self, location_id: int, time_step: int) -> List[int]:
        """获取可见卫星列表"""
        # 待实现：从轨道模块获取可见性信息
        raise NotImplementedError
    
    def get_satellite_status(self, satellite_id: int) -> Dict:
        """获取卫星状态"""
        # 待实现：返回卫星资源状态
        raise NotImplementedError
    
    def allocate_resources(self, satellite_id: int, task: Task) -> bool:
        """分配卫星资源"""
        # 待实现：为任务分配卫星资源
        raise NotImplementedError
    
    def release_resources(self, satellite_id: int, task_id: int):
        """释放卫星资源"""
        # 待实现：释放任务占用的资源
        raise NotImplementedError
    
    def process_task(self, satellite_id: int, task_record: TaskTrackingRecord, 
                     percentage: float) -> ProcessingNode:
        """在卫星上处理任务"""
        # 待实现：返回处理节点信息
        raise NotImplementedError


# 接口定义：与云端模块的接口
class CloudInterface:
    """云端接口"""
    
    def get_available_clouds(self, satellite_id: int, time_step: int) -> List[int]:
        """获取可用云中心列表"""
        # 待实现：从通信模块获取云连接信息
        raise NotImplementedError
    
    def get_cloud_status(self, cloud_id: int) -> Dict:
        """获取云中心状态"""
        # 待实现：返回云中心资源状态
        raise NotImplementedError
    
    def offload_to_cloud(self, cloud_id: int, task: Task) -> bool:
        """将任务卸载到云端"""
        # 待实现：执行云端卸载
        raise NotImplementedError
    
    def process_task(self, cloud_id: int, task_record: TaskTrackingRecord, 
                     percentage: float) -> ProcessingNode:
        """在云端处理任务"""
        # 待实现：返回处理节点信息
        raise NotImplementedError


# 接口定义：与通信模块的接口
class CommunicationInterface:
    """通信接口"""
    
    def get_link_quality(self, from_id: int, to_id: int, time_step: int) -> Dict:
        """获取链路质量"""
        # 待实现：从通信模块获取链路信息
        raise NotImplementedError
    
    def transmit_data(self, from_id: int, to_id: int, data_size_mb: float, 
                      time_step: int) -> Tuple[bool, float]:
        """传输数据"""
        # 待实现：返回传输是否成功和传输时间
        raise NotImplementedError


class TaskTrackingSystem:
    """任务跟踪系统主类"""
    
    def __init__(self, data_loader: TaskDataLoader = None,
                 satellite_interface: SatelliteInterface = None,
                 cloud_interface: CloudInterface = None,
                 communication_interface: CommunicationInterface = None):
        """初始化任务跟踪系统"""
        
        # 加载配置
        config_path = Path(__file__).parent / "config.yaml"
        with open(config_path, 'r', encoding='utf-8') as f:
            self.config = yaml.safe_load(f)
        
        # 数据加载器
        self.data_loader = data_loader or TaskDataLoader()
        
        # 初始化轨道更新器
        self.orbital_updater = OrbitalUpdater()
        
        # 建立location_id到索引的映射表
        self.location_id_to_index = {}
        ground_stations = self.orbital_updater.ground_stations
        for idx, station_id in enumerate(ground_stations.keys()):
            # 将station_id转换为整数作为location_id
            self.location_id_to_index[int(station_id)] = idx
        
        # 外部接口
        self.satellite_interface = satellite_interface or SatelliteInterface()
        self.cloud_interface = cloud_interface or CloudInterface()
        self.communication_interface = communication_interface or CommunicationInterface()
        
        # 重传策略
        self.retransmission_policy = RetransmissionPolicy()
        
        # 任务跟踪记录
        self.task_records: Dict[int, TaskTrackingRecord] = {}
        
        # 任务队列 - 使用set优化性能
        self.waiting_queue: set = set()
        self.retransmit_queue: Dict[int, int] = {}  # task_id -> next_retransmit_time
        
        # 统计信息
        self.metrics = {
            'active_tasks': 0,
            'processing_tasks': 0,
            'queued_tasks': 0,
            'retransmitting_tasks': 0,
            'completed_tasks': 0,
            'failed_tasks': 0,
            'timeout_tasks': 0,
            'retry_exhausted_tasks': 0
        }
        
        # 日志
        self.logger = logging.getLogger(__name__)
    
    def register_new_tasks(self, time_step: int):
        """注册新任务"""
        new_tasks = self.data_loader.get_tasks_at_timeslot(time_step)
        
        for task in new_tasks:
            # 创建跟踪记录
            record = TaskTrackingRecord(
                task_id=task.task_id,
                source_location_id=task.location_id,
                generation_time=task.generation_time,
                task_type=task.type_id,
                data_size_mb=task.data_size_mb,
                complexity_cycles_per_bit=task.complexity_cycles_per_bit,
                deadline_timestamp=task.deadline_timestamp,
                priority=task.priority,
                coordinates=task.coordinates
            )
            
            self.task_records[task.task_id] = record
            self.waiting_queue.add(task.task_id)
            self.metrics['queued_tasks'] += 1
    
    def check_timeout_tasks(self, current_time: int) -> List[int]:
        """检查超时任务"""
        timeout_tasks = []
        
        for task_id, record in self.task_records.items():
            if (record.current_status not in [TaskStatus.DELIVERED, TaskStatus.FAILED, TaskStatus.TIMEOUT]
                and current_time > record.deadline_timestamp):
                timeout_tasks.append(task_id)
        
        return timeout_tasks
    
    def mark_task_failed(self, task_id: int, reason: FailureReason):
        """标记任务失败"""
        if task_id in self.task_records:
            record = self.task_records[task_id]
            
            if reason == FailureReason.TIMEOUT:
                record.current_status = TaskStatus.TIMEOUT
                self.metrics['timeout_tasks'] += 1
            else:
                record.current_status = TaskStatus.FAILED
                self.metrics['failed_tasks'] += 1
            
            record.failure_reason = reason
            
            # 从队列中移除
            self.waiting_queue.discard(task_id)  # 使用discard避免KeyError
            if task_id in self.retransmit_queue:
                del self.retransmit_queue[task_id]
            
            self.logger.info(f"任务 {task_id} 失败: {reason.value}")
    
    def process_retransmissions(self, current_time: int):
        """处理重传队列"""
        tasks_to_retransmit = []
        
        for task_id, retry_time in list(self.retransmit_queue.items()):
            if current_time >= retry_time:
                tasks_to_retransmit.append(task_id)
        
        for task_id in tasks_to_retransmit:
            record = self.task_records[task_id]
            
            # 检查是否可以重传
            if self.retransmission_policy.should_retransmit(record, current_time):
                record.transmission_attempts += 1
                record.retransmission_count += 1
                record.current_status = TaskStatus.RETRANSMITTING
                record.last_transmission_time = current_time
                
                # 尝试接入
                if self.attempt_task_access(task_id, current_time):
                    del self.retransmit_queue[task_id]
                    self.metrics['retransmitting_tasks'] -= 1
                else:
                    # 计算下次重传时间
                    next_retry = self.retransmission_policy.calculate_next_retransmit_time(
                        current_time, record.transmission_attempts
                    )
                    self.retransmit_queue[task_id] = next_retry
                    record.retransmission_intervals.append(next_retry - current_time)
            else:
                # 超过重传次数或超时
                if record.transmission_attempts >= self.retransmission_policy.MAX_ATTEMPTS:
                    self.mark_task_failed(task_id, FailureReason.MAX_RETRIES)
                else:
                    self.mark_task_failed(task_id, FailureReason.TIMEOUT)
                
                if task_id in self.retransmit_queue:
                    del self.retransmit_queue[task_id]
    
    def attempt_task_access(self, task_id: int, current_time: int) -> bool:
        """尝试任务接入 - 使用真实的轨道可见性数据"""
        record = self.task_records[task_id]
        
        try:
            # 获取当前时隙的卫星位置
            satellites = self.orbital_updater.get_satellites_at_time(current_time)
            
            if not satellites:
                self.logger.warning(f"时隙 {current_time} 无卫星数据")
                return False
            
            # 构建卫星-地面站可见性矩阵和距离矩阵
            visibility_matrix, distance_matrix = self.orbital_updater.build_satellite_ground_visibility_matrix(
                satellites, current_time
            )
            
            # 使用映射表获取正确的索引
            if record.source_location_id not in self.location_id_to_index:
                self.logger.error(f"未知的location_id: {record.source_location_id}")
                return False
            
            ground_index = self.location_id_to_index[record.source_location_id]
            
            # 获取该地面站可见的卫星
            visible_satellite_indices = np.where(visibility_matrix[:, ground_index])[0]
            
            if len(visible_satellite_indices) == 0:
                # 无可见卫星，加入重传队列
                if record.transmission_attempts == 0:
                    record.transmission_attempts = 1
                    record.current_status = TaskStatus.WAITING_ACCESS
                    
                    next_retry = self.retransmission_policy.calculate_next_retransmit_time(
                        current_time, record.transmission_attempts
                    )
                    self.retransmit_queue[task_id] = next_retry
                    self.metrics['retransmitting_tasks'] += 1
                
                return False
            
            # 找到距离最近的卫星
            distances_to_visible = distance_matrix[visible_satellite_indices, ground_index]
            closest_idx = visible_satellite_indices[np.argmin(distances_to_visible)]
            
            # 获取卫星ID
            satellite_list = list(satellites.keys())
            selected_satellite_str = satellite_list[closest_idx]
            
            # 安全地从字符串ID提取数字ID (例如 'sat_111' -> 111)
            try:
                if isinstance(selected_satellite_str, str):
                    if selected_satellite_str.startswith('sat_'):
                        selected_satellite = int(selected_satellite_str.split('_')[1])
                    else:
                        selected_satellite = int(selected_satellite_str)
                else:
                    selected_satellite = int(selected_satellite_str)
                
                # 分配任务给最近的卫星
                record.access_satellite_id = selected_satellite
                record.current_status = TaskStatus.ASSIGNED
                record.transmission_attempts += 1
                
                min_distance = distance_matrix[closest_idx, ground_index]
                self.logger.info(f"任务 {task_id} 分配给卫星 {selected_satellite} (距离: {min_distance:.2f} km)")
                
                return True
                
            except (ValueError, IndexError) as e:
                self.logger.error(f"无法解析卫星ID {selected_satellite_str}: {e}")
                return False
            
        except Exception as e:
            self.logger.error(f"任务接入失败: {e}")
            return False
    
    def process_new_tasks(self, current_time: int):
        """处理新任务队列"""
        tasks_to_process = self.waiting_queue.copy()
        
        for task_id in tasks_to_process:
            if self.attempt_task_access(task_id, current_time):
                self.waiting_queue.discard(task_id)  # 使用discard替代remove
                self.metrics['queued_tasks'] -= 1
                self.metrics['active_tasks'] += 1
    
    def update_processing_tasks(self, current_time: int):
        """更新正在处理的任务"""
        # 获取所有正在处理的任务
        processing_tasks = [
            (task_id, record) 
            for task_id, record in self.task_records.items()
            if record.current_status in [
                TaskStatus.LOCAL_PROCESSING,
                TaskStatus.SATELLITE_OFFLOADING,
                TaskStatus.CLOUD_OFFLOADING,
                TaskStatus.PROCESSING
            ]
        ]
        
        # 获取混合仿真配置
        hybrid_config = self.config.get('hybrid_simulation', {})
        enable_splitting = hybrid_config.get('enable_task_splitting', False)
        
        for task_id, record in processing_tasks:
            # 检查是否有处理节点
            if not record.processing_nodes:
                # 创建初始处理节点（如果已分配卫星）
                if record.access_satellite_id:
                    node = ProcessingNode(
                        node_type=NodeType.SATELLITE,
                        node_id=record.access_satellite_id,
                        processing_percentage=0.0,
                        start_time=float(current_time),
                        end_time=float(current_time),
                        is_partial_processing=enable_splitting,
                        segment_ratio=1.0  # 默认完整任务
                    )
                    record.processing_nodes.append(node)
                    record.current_status = TaskStatus.LOCAL_PROCESSING
            
            # 更新处理进度
            for node in record.processing_nodes:
                if node.processing_percentage < 100.0:
                    # 基于卫星计算能力计算处理进度
                    computation_config = self.config.get('computation', {})
                    f_leo = computation_config.get('f_leo_hz', 10000e9)
                    
                    # 计算每时隙可处理的比例
                    total_cycles = record.complexity_cycles_per_bit * record.data_size_mb * 8 * 1024 * 1024
                    cycles_per_timeslot = f_leo * self.metadata['timeslot_duration_seconds']
                    progress_increment = min((cycles_per_timeslot / total_cycles) * 100.0, 100.0)
                    
                    # 如果是部分处理，考虑分片比例
                    if node.is_partial_processing:
                        progress_increment *= node.segment_ratio
                    
                    node.processing_percentage = min(
                        node.processing_percentage + progress_increment,
                        100.0
                    )
                    node.end_time = float(current_time)
                    node.processing_time = node.end_time - node.start_time
                    
                    # 更新能耗
                    zeta_leo = computation_config.get('zeta_leo', 1.0e-28)
                    node.energy_consumption += zeta_leo * cycles_per_timeslot
                    node.cpu_cycles_used += cycles_per_timeslot
            
            # 计算总体完成度
            total_completion = sum(node.processing_percentage * node.segment_ratio 
                                 for node in record.processing_nodes)
            record.completion_percentage = min(total_completion, 100.0)
            
            # 如果任务完成，更新状态
            if record.completion_percentage >= 100.0:
                record.current_status = TaskStatus.RETURNING
                record.delivery_time = current_time + 1  # 下一时隙交付
                self.metrics['processing_tasks'] -= 1
                
                self.logger.info(
                    f"Task {task_id} processing complete at {current_time}, "
                    f"progress: {record.completion_percentage:.1f}%"
                )
    
    def deliver_completed_tasks(self, current_time: int):
        """交付完成的任务"""
        # 查找需要交付的任务
        tasks_to_deliver = [
            (task_id, record)
            for task_id, record in self.task_records.items()
            if record.current_status == TaskStatus.RETURNING 
            and record.delivery_time == current_time
        ]
        
        delivered_count = 0
        
        for task_id, record in tasks_to_deliver:
            # 标记为已交付
            record.current_status = TaskStatus.DELIVERED
            record.is_delivered = True
            
            # 检查是否满足截止时间
            record.meet_deadline = current_time <= record.deadline_timestamp
            
            # 计算总时间消耗
            record.total_processing_time = float(current_time - record.generation_time)
            
            # 计算总传输时间（简化计算）
            if record.processing_nodes:
                record.total_transmission_time = sum(
                    node.offload_transmission_time + node.result_transmission_time
                    for node in record.processing_nodes
                )
            
            # 计算总能耗
            record.total_energy_consumption = sum(
                node.energy_consumption for node in record.processing_nodes
            )
            
            # 计算总计算资源使用
            record.total_computation_used = sum(
                node.cpu_cycles_used for node in record.processing_nodes
            )
            
            # 更新统计
            self.metrics['completed_tasks'] += 1
            self.metrics['active_tasks'] -= 1
            
            delivered_count += 1
            
            # 记录任务分割处理情况（如果有）
            if len(record.processing_nodes) > 1:
                nodes_info = ', '.join([
                    f"{node.node_type.value}_{node.node_id}({node.segment_ratio*100:.1f}%)"
                    for node in record.processing_nodes
                ])
                self.logger.info(
                    f"Task {task_id} delivered (split processing: {nodes_info}), "
                    f"meet_deadline: {record.meet_deadline}"
                )
            else:
                self.logger.info(
                    f"Task {task_id} delivered at time {current_time}, "
                    f"meet_deadline: {record.meet_deadline}"
                )
        
        return delivered_count
    
    def collect_metrics(self) -> Dict:
        """收集统计指标"""
        return self.metrics.copy()
    
    def simulate_step(self, time_step: int):
        """执行一个仿真步骤"""
        # 1. 注册新任务
        self.register_new_tasks(time_step)
        
        # 2. 检查超时任务
        timeout_tasks = self.check_timeout_tasks(time_step)
        for task_id in timeout_tasks:
            self.mark_task_failed(task_id, FailureReason.TIMEOUT)
        
        # 3. 处理重传队列
        self.process_retransmissions(time_step)
        
        # 4. 处理新任务
        self.process_new_tasks(time_step)
        
        # 5. 更新处理中的任务
        self.update_processing_tasks(time_step)
        
        # 6. 交付完成的任务
        self.deliver_completed_tasks(time_step)
        
        # 7. 返回当前指标
        return self.collect_metrics()
    
    def get_task_record(self, task_id: int) -> Optional[TaskTrackingRecord]:
        """获取任务跟踪记录"""
        return self.task_records.get(task_id)
    
    def get_statistics(self) -> Dict:
        """获取统计信息"""
        total_tasks = len(self.task_records)
        if total_tasks == 0:
            return {}
        
        completed = sum(1 for r in self.task_records.values() if r.is_delivered)
        failed = sum(1 for r in self.task_records.values() 
                    if r.current_status in [TaskStatus.FAILED, TaskStatus.TIMEOUT])
        
        # 计算重传统计
        total_attempts = sum(r.transmission_attempts for r in self.task_records.values())
        retransmitted = sum(1 for r in self.task_records.values() if r.retransmission_count > 0)
        
        stats = {
            'total_tasks': total_tasks,
            'completed_tasks': completed,
            'failed_tasks': failed,
            'completion_rate': completed / total_tasks if total_tasks > 0 else 0,
            'failure_rate': failed / total_tasks if total_tasks > 0 else 0,
            'avg_transmission_attempts': total_attempts / total_tasks if total_tasks > 0 else 0,
            'retransmission_rate': retransmitted / total_tasks if total_tasks > 0 else 0,
            'first_attempt_success_rate': (total_tasks - retransmitted) / total_tasks if total_tasks > 0 else 0
        }
        
        # 失败原因分布
        failure_breakdown = {}
        for r in self.task_records.values():
            if r.failure_reason:
                reason = r.failure_reason.value
                failure_breakdown[reason] = failure_breakdown.get(reason, 0) + 1
        
        if failed > 0:
            for reason in failure_breakdown:
                failure_breakdown[reason] = failure_breakdown[reason] / failed
        
        stats['failure_breakdown'] = failure_breakdown
        
        return stats


if __name__ == "__main__":
    # 基本测试
    logging.basicConfig(level=logging.INFO)
    
    # 创建任务跟踪系统
    tracker = TaskTrackingSystem()
    
    # 运行几个时隙的仿真
    for t in range(5):
        metrics = tracker.simulate_step(t)
        print(f"时隙 {t}: {metrics}")
    
    # 打印统计信息
    stats = tracker.get_statistics()
    print("\n统计信息:")
    for key, value in stats.items():
        print(f"  {key}: {value}")